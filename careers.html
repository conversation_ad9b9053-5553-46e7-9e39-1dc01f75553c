<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Careers at Growwell | Join Our Finance Team</title>
    <meta
      name="description"
      content="Join the Growwell team and build a rewarding career in financial services. Explore open roles and grow your future with a trusted investment firm."
    />
    <meta
      name="keywords"
      content="growwell, finance, finance department, financial help, financial services, financial expert"
    />
    <meta name="robots" content="careers, follow" />
    <link rel="canonical" href="https://growwellimf.com/" />
    <meta
      property="og:title"
      content="Careers at Growwell | Join Our Finance Team"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://growwellimf.com/" />
    <meta
      property="og:image"
      content="https://growwellimf.com/images/growwell_logo_01.webp"
    />
    <meta name="author" content="Growwell" />
    <meta name="publisher" content="Growwell" />

    <!--google adsense-->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>

    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/career.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Header Section -->
    <header>
      <div class="container">
        <div class="navbar">
          <div class="logo">
            <a href="index.html">
              <img
                src="./images/growwell_logo_01.webp"
                alt="company_logo"
                class="brand-logo"
            /></a>
            <a href="index.html"
              ><h2><span class="highlight">Grow</span>well</h2></a
            >
          </div>

          <div class="hamburger" id="hamburger">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
          </div>

          <nav id="nav-menu">
            <ul id="menu-items">
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Products</a>
                <div class="dropdown-menu">
                  <a href="investment-plans.html">Investment Plans</a>
                  <a href="retirement-plans.html">Retirement Plans</a>
                  <a href="child-plans.html">Child Plans</a>
                  <a href="term-insurance.html">Term Insurance</a>
                  <a href="tax-advisory.html">Tax Advisory</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Renew</a>
                <div class="dropdown-menu">
                  <a href="renew-policy.html">Renew Policy</a>
                  <a href="check-status.html">Check Status</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Claim</a>
                <div class="dropdown-menu">
                  <a href="file-claim.html">File a Claim</a>
                  <a href="track-claim.html">Track Claim</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Help & Support</a>
                <div class="dropdown-menu">
                  <a href="faqs.html">FAQs</a>
                  <a href="contact.html">Contact Us</a>
                  <a href="support.html">Customer Support</a>
                  <a href="advisor.html">Find an Advisor</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">About Us</a>
                <div class="dropdown-menu">
                  <a href="our-story.html">Our Story</a>
                  <a href="leadership.html">Leadership</a>
                  <a href="careers.html">Careers</a>
                </div>
              </li>
            </ul>
          </nav>

          <div class="navbar-actions desktop-only">
            <a href="investment-plans.html" class="btn-primary">Get Started</a>
          </div>
        </div>
      </div>
    </header>
    <!-- Hero Section -->
    <section class="careers-hero">
      <div class="container">
        <div class="careers-hero-content">
          <h1>Join Our Team</h1>
          <p>
            Be part of a dynamic team that's transforming financial planning in
            India. Discover exciting career opportunities at Growwell.
          </p>
          <div class="careers-hero-buttons">
            <a href="#jobs" class="btn-primary">View Open Positions</a>
            <a href="#culture" class="btn-secondary"
              ><i class="fas fa-users"></i> Our Culture</a
            >
          </div>
        </div>
      </div>
    </section>

    <!-- Jobs Section -->
    <section id="jobs" class="jobs-section">
      <div class="container">
        <div class="section-header">
          <span class="section-subtitle">Opportunities</span>
          <h2>Current <span class="open-txt"> Openings </span></h2>
          <p>Find your perfect role in our growing team</p>
        </div>

        <div class="jobs-filter">
          <button class="filter-btn active">All Departments</button>
          <button class="filter-btn">Finance</button>
          <button class="filter-btn">Technology</button>
          <button class="filter-btn">Marketing</button>
          <button class="filter-btn">Customer Service</button>
        </div>

        <div class="jobs-grid">
          <div class="job-card">
            <div class="job-header">
              <h3 class="job-title">Senior Financial Advisor</h3>
              <span class="job-department">Finance</span>
              <div class="job-location">
                <i class="fas fa-map-marker-alt"></i> Mumbai, India
              </div>
            </div>
            <div class="job-body">
              <p class="job-description">
                We're looking for an experienced Financial Advisor to join our
                team and provide expert financial guidance to our high-net-worth
                clients.
              </p>
              <div class="job-requirements">
                <h4>Requirements:</h4>
                <ul class="requirements-list">
                  <li>5+ years of experience in financial advisory</li>
                  <li>CFP certification or equivalent</li>
                  <li>Strong communication and interpersonal skills</li>
                  <li>Experience with wealth management software</li>
                </ul>
              </div>
            </div>
            <div class="job-footer">
              <div class="job-type">
                <i class="fas fa-briefcase"></i> Full-time
              </div>
              <button class="apply-btn" onclick="openApplicationModal('Senior Financial Advisor', 'Finance', 'Mumbai, India')">Apply Now</button>
            </div>
          </div>

          <div class="job-card">
            <div class="job-header">
              <h3 class="job-title">Full Stack Developer</h3>
              <span class="job-department">Technology</span>
              <div class="job-location">
                <i class="fas fa-map-marker-alt"></i> Bangalore, India
              </div>
            </div>
            <div class="job-body">
              <p class="job-description">
                Join our tech team to build and enhance our digital platforms
                that help clients manage their financial portfolios effectively.
              </p>
              <div class="job-requirements">
                <h4>Requirements:</h4>
                <ul class="requirements-list">
                  <li>3+ years of experience in full stack development</li>
                  <li>Proficiency in React, Node.js, and MongoDB</li>
                  <li>Experience with RESTful APIs and microservices</li>
                  <li>Knowledge of financial technology is a plus</li>
                </ul>
              </div>
            </div>
            <div class="job-footer">
              <div class="job-type">
                <i class="fas fa-briefcase"></i> Full-time
              </div>
              <button class="apply-btn" onclick="openApplicationModal('Full Stack Developer', 'Technology', 'Bangalore, India')">Apply Now</button>
            </div>
          </div>

          <div class="job-card">
            <div class="job-header">
              <h3 class="job-title">Digital Marketing Specialist</h3>
              <span class="job-department">Marketing</span>
              <div class="job-location">
                <i class="fas fa-map-marker-alt"></i> Delhi, India
              </div>
            </div>
            <div class="job-body">
              <p class="job-description">
                Help us grow our digital presence and reach more clients through
                innovative marketing strategies and campaigns.
              </p>
              <div class="job-requirements">
                <h4>Requirements:</h4>
                <ul class="requirements-list">
                  <li>2+ years of experience in digital marketing</li>
                  <li>Expertise in SEO, SEM, and social media marketing</li>
                  <li>Experience with marketing analytics tools</li>
                  <li>Strong content creation skills</li>
                </ul>
              </div>
            </div>
            <div class="job-footer">
              <div class="job-type">
                <i class="fas fa-briefcase"></i> Full-time
              </div>
              <button class="apply-btn" onclick="openApplicationModal('Digital Marketing Specialist', 'Marketing', 'Delhi, India')">Apply Now</button>
            </div>
          </div>

          <div class="job-card">
            <div class="job-header">
              <h3 class="job-title">Customer Support Executive</h3>
              <span class="job-department">Customer Service</span>
              <div class="job-location">
                <i class="fas fa-map-marker-alt"></i> Pune, India
              </div>
            </div>
            <div class="job-body">
              <p class="job-description">
                Be the voice of Growwell and provide exceptional support to our
                clients, helping them navigate their financial journey with us.
              </p>
              <div class="job-requirements">
                <h4>Requirements:</h4>
                <ul class="requirements-list">
                  <li>1+ years of experience in customer service</li>
                  <li>Excellent communication skills in English and Hindi</li>
                  <li>Basic understanding of financial products</li>
                  <li>Problem-solving attitude and patience</li>
                </ul>
              </div>
            </div>
            <div class="job-footer">
              <div class="job-type">
                <i class="fas fa-briefcase"></i> Full-time
              </div>
              <button class="apply-btn" onclick="openApplicationModal('Customer Support Executive', 'Customer Service', 'Pune, India')">Apply Now</button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Benefits Section -->
    <section class="benefits-section">
      <div class="container">
        <div class="section-header">
          <span class="section-subtitle">Why Join Us</span>
          <h2><span class="employee-txt"> Employee </span> Benefits</h2>
          <p>
            We value our team members and offer competitive benefits to ensure
            their well-being
          </p>
        </div>

        <div class="benefits-grid">
          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-heart"></i>
            </div>
            <h3>Health & Wellness</h3>
            <p>
              Comprehensive health insurance for you and your family, wellness
              programs, and mental health support.
            </p>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3>Career Growth</h3>
            <p>
              Continuous learning opportunities, mentorship programs, and clear
              career advancement paths.
            </p>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-coins"></i>
            </div>
            <h3>Financial Benefits</h3>
            <p>
              Competitive salary, performance bonuses, and employee investment
              programs with special rates.
            </p>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <h3>Work-Life Balance</h3>
            <p>
              Flexible working hours, work-from-home options, and generous paid
              time off policies.
            </p>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-graduation-cap"></i>
            </div>
            <h3>Education Support</h3>
            <p>
              Tuition reimbursement for relevant certifications and advanced
              degrees in finance and technology.
            </p>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-users"></i>
            </div>
            <h3>Team Events</h3>
            <p>
              Regular team outings, celebrations, and annual retreats to foster
              a strong company culture.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Culture Section -->
    <section id="culture" class="culture-section">
      <div class="container">
        <div class="section-header">
          <span class="section-subtitle">Our Environment</span>
          <h2>Company <span class="culture-txt"> Culture </span></h2>
          <p>
            Experience a workplace that values innovation, collaboration, and
            personal growth
          </p>
        </div>

        <div class="culture-content">
          <div class="culture-text">
            <h3>What Makes Growwell Special</h3>
            <p>
              At Growwell, we believe that our culture is our greatest asset.
              We've built an environment where innovation thrives, diverse
              perspectives are valued, and everyone has the opportunity to make
              a meaningful impact.
            </p>
            <p>
              Our team members describe Growwell as a place where they can grow
              both professionally and personally while contributing to our
              mission of transforming financial planning in India.
            </p>

            <div class="culture-values">
              <div class="culture-value">
                <div class="value-icon">
                  <i class="fas fa-lightbulb"></i>
                </div>
                <div class="value-text">
                  <h4>Innovation</h4>
                  <p>
                    We encourage creative thinking and new ideas at all levels
                    of the organization.
                  </p>
                </div>
              </div>

              <div class="culture-value">
                <div class="value-icon">
                  <i class="fas fa-hands-helping"></i>
                </div>
                <div class="value-text">
                  <h4>Collaboration</h4>
                  <p>
                    We work together across departments to achieve common goals
                    and deliver exceptional results.
                  </p>
                </div>
              </div>

              <div class="culture-value">
                <div class="value-icon">
                  <i class="fas fa-award"></i>
                </div>
                <div class="value-text">
                  <h4>Excellence</h4>
                  <p>
                    We strive for excellence in everything we do, from client
                    service to product development.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="culture-gallery">
            <div class="gallery-item">
              <i class="fas fa-users"></i>
              <div class="gallery-caption">Team Collaboration</div>
            </div>
            <div class="gallery-item">
              <i class="fas fa-laptop-code"></i>
              <div class="gallery-caption">Innovation Hub</div>
            </div>
            <div class="gallery-item">
              <i class="fas fa-coffee"></i>
              <div class="gallery-caption">Break Time</div>
            </div>
            <div class="gallery-item">
              <i class="fas fa-trophy"></i>
              <div class="gallery-caption">Celebrating Success</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Job Application Modal -->
    <div id="applicationModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2 id="applicationModalTitle">Apply for Position</h2>
          <span class="close" onclick="closeApplicationModal()">&times;</span>
        </div>
        <div class="modal-body">
          <form id="applicationForm" class="application-form" enctype="multipart/form-data">
            <div class="form-row">
              <div class="form-group">
                <label for="jobTitle">Position Applied For *</label>
                <input type="text" id="jobTitle" name="jobTitle" readonly>
              </div>
              <div class="form-group">
                <label for="jobDepartment">Department</label>
                <input type="text" id="jobDepartment" name="jobDepartment" readonly>
              </div>
            </div>

            <div class="form-section">
              <h3><i class="fas fa-user"></i> Personal Information</h3>
              <div class="form-row">
                <div class="form-group">
                  <label for="firstName">First Name *</label>
                  <input type="text" id="firstName" name="firstName" required>
                </div>
                <div class="form-group">
                  <label for="lastName">Last Name *</label>
                  <input type="text" id="lastName" name="lastName" required>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="email">Email Address *</label>
                  <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                  <label for="phone">Phone Number *</label>
                  <input type="tel" id="phone" name="phone" required>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="currentLocation">Current Location *</label>
                  <input type="text" id="currentLocation" name="currentLocation" placeholder="City, State" required>
                </div>
                <div class="form-group">
                  <label for="dateOfBirth">Date of Birth</label>
                  <input type="date" id="dateOfBirth" name="dateOfBirth">
                </div>
              </div>
            </div>

            <div class="form-section">
              <h3><i class="fas fa-briefcase"></i> Professional Information</h3>
              <div class="form-row">
                <div class="form-group">
                  <label for="currentCompany">Current Company</label>
                  <input type="text" id="currentCompany" name="currentCompany">
                </div>
                <div class="form-group">
                  <label for="currentPosition">Current Position</label>
                  <input type="text" id="currentPosition" name="currentPosition">
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="totalExperience">Total Experience *</label>
                  <select id="totalExperience" name="totalExperience" required>
                    <option value="">Select Experience</option>
                    <option value="0-1">0-1 years</option>
                    <option value="1-3">1-3 years</option>
                    <option value="3-5">3-5 years</option>
                    <option value="5-10">5-10 years</option>
                    <option value="10+">10+ years</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="currentSalary">Current Salary (Annual)</label>
                  <select id="currentSalary" name="currentSalary">
                    <option value="">Select Range</option>
                    <option value="0-3">0-3 Lakhs</option>
                    <option value="3-6">3-6 Lakhs</option>
                    <option value="6-10">6-10 Lakhs</option>
                    <option value="10-15">10-15 Lakhs</option>
                    <option value="15-25">15-25 Lakhs</option>
                    <option value="25+">25+ Lakhs</option>
                  </select>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="expectedSalary">Expected Salary (Annual) *</label>
                  <select id="expectedSalary" name="expectedSalary" required>
                    <option value="">Select Range</option>
                    <option value="3-6">3-6 Lakhs</option>
                    <option value="6-10">6-10 Lakhs</option>
                    <option value="10-15">10-15 Lakhs</option>
                    <option value="15-25">15-25 Lakhs</option>
                    <option value="25-35">25-35 Lakhs</option>
                    <option value="35+">35+ Lakhs</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="noticePeriod">Notice Period *</label>
                  <select id="noticePeriod" name="noticePeriod" required>
                    <option value="">Select Period</option>
                    <option value="immediate">Immediate</option>
                    <option value="15-days">15 Days</option>
                    <option value="1-month">1 Month</option>
                    <option value="2-months">2 Months</option>
                    <option value="3-months">3 Months</option>
                  </select>
                </div>
              </div>
            </div>

            <div class="form-section">
              <h3><i class="fas fa-graduation-cap"></i> Education & Skills</h3>
              <div class="form-row">
                <div class="form-group">
                  <label for="highestQualification">Highest Qualification *</label>
                  <select id="highestQualification" name="highestQualification" required>
                    <option value="">Select Qualification</option>
                    <option value="12th">12th Pass</option>
                    <option value="diploma">Diploma</option>
                    <option value="bachelor">Bachelor's Degree</option>
                    <option value="master">Master's Degree</option>
                    <option value="phd">PhD</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="fieldOfStudy">Field of Study</label>
                  <input type="text" id="fieldOfStudy" name="fieldOfStudy" placeholder="e.g., Computer Science, Finance">
                </div>
              </div>

              <div class="form-group">
                <label for="keySkills">Key Skills *</label>
                <textarea id="keySkills" name="keySkills" rows="3" placeholder="List your key skills separated by commas" required></textarea>
              </div>

              <div class="form-group">
                <label for="certifications">Certifications (if any)</label>
                <textarea id="certifications" name="certifications" rows="2" placeholder="List any relevant certifications"></textarea>
              </div>
            </div>

            <div class="form-section">
              <h3><i class="fas fa-file-upload"></i> Documents</h3>
              <div class="form-row">
                <div class="form-group">
                  <label for="resume">Resume/CV *</label>
                  <input type="file" id="resume" name="resume" accept=".pdf,.doc,.docx" required>
                  <small>Accepted formats: PDF, DOC, DOCX (Max 5MB)</small>
                </div>
                <div class="form-group">
                  <label for="coverLetter">Cover Letter</label>
                  <input type="file" id="coverLetter" name="coverLetter" accept=".pdf,.doc,.docx">
                  <small>Optional (Max 5MB)</small>
                </div>
              </div>
            </div>

            <div class="form-section">
              <h3><i class="fas fa-question-circle"></i> Additional Information</h3>
              <div class="form-group">
                <label for="whyJoin">Why do you want to join Growwell? *</label>
                <textarea id="whyJoin" name="whyJoin" rows="4" placeholder="Tell us what motivates you to join our team..." required></textarea>
              </div>

              <div class="form-group">
                <label for="additionalInfo">Additional Information</label>
                <textarea id="additionalInfo" name="additionalInfo" rows="3" placeholder="Any additional information you'd like to share..."></textarea>
              </div>
            </div>

            <div class="form-actions">
              <button type="button" class="btn-outline" onclick="closeApplicationModal()">Cancel</button>
              <button type="submit" class="btn-primary">Submit Application</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Footer Section -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <div class="footer-logo-img">
              <img
                src="./images/growwell_logo_01.webp"
                alt="Growwell Logo"
                loading="lazy"
                class="footer-logo-image"
              />
            </div>
            <p>Your partner for financial growth and security</p>
            <div class="social-icons">
              <a
                href="https://www.facebook.com/growwellimf/"
                target="/"
                aria-label="Follow us on Facebook"
                ><i class="fab fa-facebook-f"></i
              ></a>
              <a
                href="https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08"
                target="/"
                aria-label="Follow us on X"
                ><i class="fab fa-twitter"></i
              ></a>
              <a
                href="https://www.linkedin.com/company/grow-well-imf/about/"
                target="/"
                aria-label="Follow us on Linkedin"
                ><i class="fab fa-linkedin-in"></i
              ></a>
              <a
                href="https://www.instagram.com/growwell.imf/"
                target="/"
                aria-label="Follow us on Instagram"
                ><i class="fab fa-instagram"></i
              ></a>
            </div>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h3>Quick Links</h3>
              <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="./investment-plans.html">Products</a></li>
                <li><a href="./renew-policy.html">Renew</a></li>
                <li><a href="./advisor.html">Testimonials</a></li>
                <li><a href="./contact.html">Contact</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Services</h3>
              <ul>
                <li><a href="investment-plans.html">Financial Planning</a></li>
                <li><a href="child-plans.html">Child Planning</a></li>
                <li><a href="investment-plans.html">Investment Planning</a></li>
                <li><a href="retirement-plans.html">Retirement Planning</a></li>
                <li><a href="term-insurance.html">Term Insurance</a></li>
                <li><a href="#">Tax Advisory</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Resources</h3>
              <ul>
                <li><a href="leadership.html">Leadership</a></li>
                <li><a href="advisor.html">Advisor</a></li>
                <li>
                  <a href="retirement-plans.html">Financial Calculators</a>
                </li>
                <li><a href="faqs.html">FAQs</a></li>
                <li><a href="contact.html">contact</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2023 Growwell Financial Advisors. All rights reserved.</p>
          <ul class="footer-legal">
            <li><a href="privacy-policy.html">Privacy Policy</a></li>
            <li><a href="terms-of-service.html">Terms of Service</a></li>
            <li><a href="cookie-policy.html">Cookie Policy</a></li>
          </ul>
        </div>
      </div>
    </footer>

    <script src="js/script.js"></script>

    <script>
        // Job Application Modal Functions
        function openApplicationModal(jobTitle, department, location) {
            document.getElementById('jobTitle').value = jobTitle;
            document.getElementById('jobDepartment').value = department;
            document.getElementById('applicationModalTitle').textContent = `Apply for ${jobTitle}`;
            document.getElementById('applicationModal').style.display = 'block';

            // Reset form
            document.getElementById('applicationForm').reset();
            document.getElementById('jobTitle').value = jobTitle;
            document.getElementById('jobDepartment').value = department;
        }

        function closeApplicationModal() {
            document.getElementById('applicationModal').style.display = 'none';
            document.getElementById('applicationForm').reset();
        }

        // Handle form submission
        document.getElementById('applicationForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Validate file sizes
            const resume = document.getElementById('resume').files[0];
            const coverLetter = document.getElementById('coverLetter').files[0];

            if (resume && resume.size > 5 * 1024 * 1024) {
                alert('Resume file size should not exceed 5MB');
                return;
            }

            if (coverLetter && coverLetter.size > 5 * 1024 * 1024) {
                alert('Cover letter file size should not exceed 5MB');
                return;
            }

            // Collect form data
            const formData = new FormData(this);
            const applicationData = {};

            // Convert FormData to object for display
            for (let [key, value] of formData.entries()) {
                if (key === 'resume' || key === 'coverLetter') {
                    if (value.name) {
                        applicationData[key] = value.name;
                    }
                } else {
                    applicationData[key] = value;
                }
            }

            // Simulate sending email to company
            const emailContent = generateEmailContent(applicationData);

            // Show success message
            alert(`Application submitted successfully!

Your application for ${applicationData.jobTitle} has been received.

Application Details:
• Name: ${applicationData.firstName} ${applicationData.lastName}
• Email: ${applicationData.email}
• Phone: ${applicationData.phone}
• Experience: ${applicationData.totalExperience}
• Expected Salary: ${applicationData.expectedSalary} Lakhs

Our HR team will review your application and contact you within 3-5 business days.

Thank you for your interest in joining Growwell!`);

            // Log email content (in real implementation, this would be sent to company email)
            console.log('Email Content to be sent to company:', emailContent);

            closeApplicationModal();
        });

        function generateEmailContent(data) {
            return `
New Job Application Received - ${data.jobTitle}

POSITION DETAILS:
• Job Title: ${data.jobTitle}
• Department: ${data.jobDepartment}
• Application Date: ${new Date().toLocaleDateString()}

PERSONAL INFORMATION:
• Name: ${data.firstName} ${data.lastName}
• Email: ${data.email}
• Phone: ${data.phone}
• Current Location: ${data.currentLocation}
• Date of Birth: ${data.dateOfBirth || 'Not provided'}

PROFESSIONAL INFORMATION:
• Current Company: ${data.currentCompany || 'Not provided'}
• Current Position: ${data.currentPosition || 'Not provided'}
• Total Experience: ${data.totalExperience}
• Current Salary: ${data.currentSalary || 'Not disclosed'} Lakhs
• Expected Salary: ${data.expectedSalary} Lakhs
• Notice Period: ${data.noticePeriod}

EDUCATION & SKILLS:
• Highest Qualification: ${data.highestQualification}
• Field of Study: ${data.fieldOfStudy || 'Not provided'}
• Key Skills: ${data.keySkills}
• Certifications: ${data.certifications || 'None mentioned'}

DOCUMENTS:
• Resume: ${data.resume || 'Not uploaded'}
• Cover Letter: ${data.coverLetter || 'Not uploaded'}

ADDITIONAL INFORMATION:
• Why join Growwell: ${data.whyJoin}
• Additional Info: ${data.additionalInfo || 'None provided'}

---
This application was submitted through the Growwell Careers portal.
Please review and respond to the candidate within 3-5 business days.
            `;
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('applicationModal');
            if (event.target === modal) {
                closeApplicationModal();
            }
        }

        // File upload validation
        document.getElementById('resume').addEventListener('change', function(e) {
            validateFileUpload(e.target, 'resume');
        });

        document.getElementById('coverLetter').addEventListener('change', function(e) {
            validateFileUpload(e.target, 'cover letter');
        });

        function validateFileUpload(input, fileType) {
            const file = input.files[0];
            if (file) {
                const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
                const maxSize = 5 * 1024 * 1024; // 5MB

                if (!allowedTypes.includes(file.type)) {
                    alert(`Please upload a valid ${fileType} file (PDF, DOC, or DOCX)`);
                    input.value = '';
                    return;
                }

                if (file.size > maxSize) {
                    alert(`${fileType} file size should not exceed 5MB`);
                    input.value = '';
                    return;
                }
            }
        }

        // Department filter functionality
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');

                const filterValue = this.textContent.trim();
                const jobCards = document.querySelectorAll('.job-card');

                jobCards.forEach(card => {
                    const department = card.querySelector('.job-department').textContent.trim();

                    if (filterValue === 'All Departments' || department === filterValue) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    </script>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Growwell",
        "url": "https://growwellimf.com",
        "logo": "https://growwellimf.com/images/growwell_logo_01.webp",
        "sameAs": [
          "https://www.facebook.com/growwellimf/",
          "https://www.instagram.com/growwell.imf/",
          "https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08",
          "https://www.linkedin.com/company/grow-well-imf/about/"
        ]
      }
    </script>
  </body>
</html>
