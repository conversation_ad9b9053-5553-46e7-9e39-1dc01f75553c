<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Contact Growwell | Financial Help & Support</title>
    <meta
      name="description"
      content="Get in touch with Growwell financial experts today. We’re here to answer your questions and help you plan your financial future with confidence."
    />
    <meta
      name="keywords"
      content="growwell, financial experts, financial future, financial help"
    />
    <meta name="robots" content="contact, follow" />
    <link rel="canonical" href="https://growwellimf.com/" />
    <meta
      property="og:title"
      content="Contact Growwell | Financial Help & Support"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://growwellimf.com/" />
    <meta
      property="og:image"
      content="https://growwellimf.com/images/growwell_logo_01.webp"
    />
    <meta name="author" content="Growwell" />
    <meta name="publisher" content="Growwell" />

    <!--google adsense-->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>

    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/contact.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Header Section -->
    <header>
      <div class="container">
        <div class="navbar">
          <div class="logo">
            <a href="index.html">
              <img
                src="./images/growwell_logo_01.webp"
                alt="company_logo"
                class="brand-logo"
            /></a>
            <a href="index.html"
              ><h2><span class="highlight">Grow</span>well</h2></a
            >
          </div>

          <div class="hamburger" id="hamburger">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
          </div>

          <nav id="nav-menu">
            <ul id="menu-items">
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Products</a>
                <div class="dropdown-menu">
                  <a href="investment-plans.html">Investment Plans</a>
                  <a href="retirement-plans.html">Retirement Plans</a>
                  <a href="child-plans.html">Child Plans</a>
                  <a href="term-insurance.html">Term Insurance</a>
                  <a href="tax-advisory.html">Tax Advisory</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Renew</a>
                <div class="dropdown-menu">
                  <a href="renew-policy.html">Renew Policy</a>
                  <a href="check-status.html">Check Status</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Claim</a>
                <div class="dropdown-menu">
                  <a href="file-claim.html">File a Claim</a>
                  <a href="track-claim.html">Track Claim</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Help & Support</a>
                <div class="dropdown-menu">
                  <a href="faqs.html">FAQs</a>
                  <a href="contact.html">Contact Us</a>
                  <a href="support.html">Customer Support</a>
                  <a href="advisor.html">Find an Advisor</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">About Us</a>
                <div class="dropdown-menu">
                  <a href="our-story.html">Our Story</a>
                  <a href="leadership.html">Leadership</a>
                  <a href="careers.html">Careers</a>
                </div>
              </li>
            </ul>
          </nav>

          <div class="navbar-actions desktop-only">
            <a href="investment-plans.html" class="btn-primary">Get Started</a>
          </div>
        </div>
      </div>
    </header>
    <!-- Contact Hero Section -->
    <section class="contact-hero">
      <div class="container">
        <div class="contact-hero-content">
          <h1>Contact Us</h1>
          <p>Get in touch with our team for personalized financial guidance.</p>
        </div>
      </div>
    </section>

    <!-- Contact Main Section -->
    <section class="contact-section">
      <div class="container">
        <div class="contact-container">
          <div class="contact-form-container">
            <div class="form-success" id="formSuccess">
              <p>
                <strong>Thank you for contacting us!</strong> We've received
                your message and will get back to you shortly.
              </p>
            </div>

            <h2>Send Us a Message</h2>
            <form id="contactForm">
              <div class="form-group">
                <label for="name">Full Name</label>
                <input
                  type="text"
                  id="name"
                  class="form-control"
                  placeholder="Enter your full name"
                  required
                />
              </div>

              <div class="form-group">
                <label for="email">Email Address</label>
                <input
                  type="email"
                  id="email"
                  class="form-control"
                  placeholder="Enter your email address"
                  required
                />
              </div>

              <div class="form-group">
                <label for="phone">Phone Number</label>
                <input
                  type="tel"
                  id="phone"
                  class="form-control"
                  placeholder="Enter your phone number"
                />
              </div>

              <div class="form-group">
                <label for="subject">Subject</label>
                <select id="subject" class="form-control" required>
                  <option value="" selected disabled>Select a subject</option>
                  <option value="Investment Inquiry">Investment Inquiry</option>
                  <option value="Insurance Inquiry">Insurance Inquiry</option>
                  <option value="Retirement Planning">
                    Retirement Planning
                  </option>
                  <option value="Tax Planning">Tax Planning</option>
                  <option value="General Inquiry">General Inquiry</option>
                  <option value="Feedback">Feedback</option>
                </select>
              </div>

              <div class="form-group">
                <label for="message">Message</label>
                <textarea
                  id="message"
                  class="form-control"
                  placeholder="Enter your message"
                  required
                ></textarea>
              </div>

              <button type="submit" class="message-btn">Send Message</button>
            </form>
          </div>

          <div class="contact-info">
            <h2>Contact Information</h2>

            <div class="contact-info-item">
              <div class="contact-info-icon">
                <i class="fas fa-phone-alt"></i>
              </div>
              <div class="contact-info-content">
                <h3>Call Us</h3>
                <p><a href="tel:+911234567890">+91 9958738161</a></p>
              </div>
            </div>

            <div class="contact-info-item">
              <div class="contact-info-icon">
                <i class="fas fa-envelope"></i>
              </div>
              <div class="contact-info-content">
                <h3>Email Us</h3>
                <p>
                  <a href="mailto:<EMAIL>"> <EMAIL></a>
                </p>
              </div>
            </div>

            <div class="contact-info-item">
              <div class="contact-info-icon">
                <i class="fas fa-clock"></i>
              </div>
              <div class="contact-info-content">
                <h3>Working Hours</h3>
                <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                <p>Saturday: 10:00 AM - 2:00 PM</p>
                <p>Sunday: Closed</p>
              </div>
            </div>

            <div class="social-links">
              <a href="#" class="social-link"
                ><i class="fab fa-facebook-f"></i
              ></a>
              <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
              <a href="#" class="social-link"
                ><i class="fab fa-linkedin-in"></i
              ></a>
              <a href="#" class="social-link"
                ><i class="fab fa-instagram"></i
              ></a>
              <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
            </div>

            <div class="map-container">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3771.1679021456!2d72.86574931469906!3d19.06463458709418!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3be7c8e123f8d27b%3A0x437996b49a236a78!2sBandra%20Kurla%20Complex%2C%20Bandra%20East%2C%20Mumbai%2C%20Maharashtra!5e0!3m2!1sen!2sin!4v1625147370548!5m2!1sen!2sin"
                allowfullscreen=""
                loading="lazy"
              ></iframe>
            </div>
          </div>
        </div>
      </div>

      <!-- Animated Shapes -->
      <div class="shape-animation contact-shape-1"></div>
      <div class="shape-animation contact-shape-2"></div>
    </section>

    <!-- Footer Section -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <div class="footer-logo-img">
              <img
                src="./images/growwell_logo_01.webp"
                alt="Growwell Logo"
                loading="lazy"
                class="footer-logo-image"
              />
            </div>
            <p>Your partner for financial growth and security</p>
            <div class="social-icons">
              <a
                href="https://www.facebook.com/growwellimf/"
                target="/"
                aria-label="Follow us on Facebook"
                ><i class="fab fa-facebook-f"></i
              ></a>
              <a
                href="https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08"
                target="/"
                aria-label="Follow us on X"
                ><i class="fab fa-twitter"></i
              ></a>
              <a
                href="https://www.linkedin.com/company/grow-well-imf/about/"
                target="/"
                aria-label="Follow us on Linkedin"
                ><i class="fab fa-linkedin-in"></i
              ></a>
              <a
                href="https://www.instagram.com/growwell.imf/"
                target="/"
                aria-label="Follow us on Instagram"
                ><i class="fab fa-instagram"></i
              ></a>
            </div>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h3>Quick Links</h3>
              <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="./investment-plans.html">Products</a></li>
                <li><a href="./renew-policy.html">Renew</a></li>
                <li><a href="./advisor.html">Testimonials</a></li>
                <li><a href="./contact.html">Contact</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Services</h3>
              <ul>
                <li><a href="investment-plans.html">Financial Planning</a></li>
                <li><a href="child-plans.html">Child Planning</a></li>
                <li><a href="investment-plans.html">Investment Planning</a></li>
                <li><a href="retirement-plans.html">Retirement Planning</a></li>
                <li><a href="term-insurance.html">Term Insurance</a></li>
                <li><a href="#">Tax Advisory</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Resources</h3>
              <ul>
                <li><a href="leadership.html">Leadership</a></li>
                <li><a href="advisor.html">Advisor</a></li>
                <li>
                  <a href="retirement-plans.html">Financial Calculators</a>
                </li>
                <li><a href="faqs.html">FAQs</a></li>
                <li><a href="contact.html">contact</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2023 Growwell Financial Advisors. All rights reserved.</p>
          <ul class="footer-legal">
            <li><a href="#">Privacy Policy</a></li>
            <li><a href="#">Terms of Service</a></li>
            <li><a href="#">Cookie Policy</a></li>
          </ul>
        </div>
      </div>
    </footer>

    <script src="js/script.js"></script>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Growwell",
        "url": "https://growwellimf.com",
        "logo": "https://growwellimf.com/images/growwell_logo_01.webp",
        "sameAs": [
          "https://www.facebook.com/growwellimf/",
          "https://www.instagram.com/growwell.imf/",
          "https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08",
          "https://www.linkedin.com/company/grow-well-imf/about/"
        ]
      }
    </script>
    <script>
      // Contact Form Functionality
      document.addEventListener("DOMContentLoaded", function () {
        const contactForm = document.getElementById("contactForm");
        const formSuccess = document.getElementById("formSuccess");

        if (contactForm) {
          contactForm.addEventListener("submit", function (e) {
            e.preventDefault();

            // Simulate form submission
            setTimeout(() => {
              formSuccess.style.display = "block";
              contactForm.reset();

              // Scroll to success message
              formSuccess.scrollIntoView({
                behavior: "smooth",
                block: "start",
              });

              // Hide success message after 5 seconds
              setTimeout(() => {
                formSuccess.style.display = "none";
              }, 5000);
            }, 1000);
          });
        }

        // Animation for form and info sections
        const animateElements = function () {
          const formContainer = document.querySelector(
            ".contact-form-container"
          );
          const infoContainer = document.querySelector(".contact-info");
          const officeCards = document.querySelectorAll(".office-card");

          if (formContainer && infoContainer) {
            formContainer.style.opacity = "0";
            formContainer.style.transform = "translateY(30px)";
            formContainer.style.transition =
              "opacity 0.6s ease, transform 0.6s ease";

            infoContainer.style.opacity = "0";
            infoContainer.style.transform = "translateY(30px)";
            infoContainer.style.transition =
              "opacity 0.6s ease 0.2s, transform 0.6s ease 0.2s";

            setTimeout(() => {
              formContainer.style.opacity = "1";
              formContainer.style.transform = "translateY(0)";
            }, 300);

            setTimeout(() => {
              infoContainer.style.opacity = "1";
              infoContainer.style.transform = "translateY(0)";
            }, 500);
          }

          officeCards.forEach((card, index) => {
            card.style.opacity = "0";
            card.style.transform = "translateY(30px)";
            card.style.transition = `opacity 0.6s ease ${
              0.4 + index * 0.1
            }s, transform 0.6s ease ${0.4 + index * 0.1}s`;

            setTimeout(() => {
              card.style.opacity = "1";
              card.style.transform = "translateY(0)";
            }, 700 + index * 100);
          });
        };

        // Trigger animations after a short delay
        setTimeout(animateElements, 300);
      });
    </script>
  </body>
</html>
