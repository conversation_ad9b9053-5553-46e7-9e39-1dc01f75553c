:root {
    --primary-color: #1e56a0;
    --primary-dark: #164584;
    --primary-light: #3b7dd4;
    --secondary-color: #163172;
    --accent-color: #f6b93b;
    --accent-dark: #e6a012;
    --dark-color: #0c2340;
    --light-color: #f4f7fc;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --gray-color: #6c757d;
    --gray-light-color: #f8f9fa;
    --shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    --shadow-intense: 0 15px 40px rgba(0, 0, 0, 0.15);
    --shadow-sm: 0 3px 12px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
    --gradient-bg: linear-gradient(135deg, rgba(244, 247, 252, 0.95) 0%, rgba(214, 229, 250, 0.95) 100%);
    
    /* Text colors */
    --text-color: #333;
    --text-light: #6c757d;
    --bg-color: #fff;
    --card-bg: #fff;
}
.contact-hero {
    background: linear-gradient(135deg, var(--accent-color), var(--success-color));

        color: #ffffff;
        padding: 100px 20px;
        text-align: center;
        /* margin-top: 100px; */
        margin-top: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
}

.contact-hero::before {
  content: "";
  position: absolute;
  width: 400px;
  height: 400px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  top: -100px;
  left: -100px;
  z-index: 0;
}

.contact-hero::after {
  content: "";
  position: absolute;
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  bottom: -80px;
  right: -80px;
  z-index: 0;
}

.message-btn{
    padding: 1rem;
    border: none;
    border-radius: 0.85rem;
    background: var(--accent-color);
    color: #fff;
}

.contact-hero .container {
  position: relative;
  z-index: 1;
  max-width: 900px;
  margin: 0 auto;
}

.contact-hero-content h1 {
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
  line-height: 1.2;
}

.contact-hero-content p {
  font-size: 1.2rem;
  color: #cfd8dc;
}

        .contact-section {
            background-color: var(--light-color);
            position: relative;
            overflow: hidden;
        }
        
        .contact-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            position: relative;
            z-index: 2;
        }
        
        @media (max-width: 992px) {
            .contact-container {
                grid-template-columns: 1fr;
            }
        }
        
        .contact-form-container {
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 40px;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        
        .contact-form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--accent-color);
        }
        
        .contact-form-container h2 {
            margin-bottom: 30px;
            color: var(--dark-color);
            position: relative;
            display: inline-block;
        }
        
        .contact-form-container h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--primary-color);
            border-radius: 2px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark-color);
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e1e1e1;
            border-radius: var(--border-radius-sm);
            font-family: 'Poppins', sans-serif;
            font-size: 16px;
            transition: var(--transition);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(30, 86, 160, 0.1);
        }
        
        textarea.form-control {
            min-height: 150px;
            resize: vertical;
        }
        
        .contact-info {
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 40px;
            height: fit-content;
        }
        
        .contact-info h2 {
            margin-bottom: 30px;
            color: var(--dark-color);
            position: relative;
            display: inline-block;
        }
        
        .contact-info h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--primary-color);
            border-radius: 2px;
        }
        
        .contact-info-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 25px;
        }
        
        .contact-info-icon {
            width: 50px;
            height: 50px;
            background: rgba(30, 86, 160, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: var(--accent-color);
            font-size: 20px;
            flex-shrink: 0;
            transition: var(--transition);
        }
        
        .contact-info-item:hover .contact-info-icon {
            background: var(--accent-color);
            color: #fff;
            transform: scale(1.1);
        }
        
        .contact-info-content h3 {
            font-size: 18px;
            margin-bottom: 5px;
            color: var(--dark-color);
        }
        
        .contact-info-content p, .contact-info-content a {
            color: var(--gray-color);
            line-height: 1.6;
            transition: var(--transition);
        }
        
        .contact-info-content a:hover {
            color: var(--primary-color);
        }
        
        .social-links {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }
        
        .social-link {
            width: 40px;
            height: 40px;
            background: rgba(30, 86, 160, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 18px;
            transition: var(--transition);
        }
        
        .social-link:hover {
            background: var(--primary-color);
            color: #fff;
            transform: translateY(-5px);
        }
        
        .map-container {
            margin-top: 40px;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            height: 300px;
        }
        
        .map-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .office-locations {
            margin-top: 60px;
        }
        
        .office-locations h2 {
            margin-bottom: 30px;
            color: var(--dark-color);
            position: relative;
            display: inline-block;
        }
        
        .office-locations h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--primary-color);
            border-radius: 2px;
        }
        
        .office-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .office-card {
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            padding: 25px;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        
        .office-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow);
        }
        
        .office-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: var(--accent-color);
        }
        
        .office-card h3 {
            margin-bottom: 15px;
            color: var(--dark-color);
            font-size: 20px;
        }
        
        .office-card p {
            color: var(--gray-color);
            margin-bottom: 10px;
            display: flex;
            align-items: flex-start;
        }
        
        .office-card p i {
            color: var(--accent-color);
            margin-right: 10px;
            margin-top: 5px;
        }
        
        .office-card .btn-outline {
            margin-top: 15px;
            display: inline-block;
            padding: 8px 20px;
            border: 1px solid var(--accent-color);
            border-radius: 30px;
            color: var(--accent-color);
            font-weight: 500;
            transition: var(--transition);
        }
        
        .office-card .btn-outline:hover {
            background: var(--accent-color);
            color: #fff;
        }
        
        .shape-animation {
            position: absolute;
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            z-index: 0;
        }
        
        .contact-shape-1 {
            top: 10%;
            left: 5%;
            width: 300px;
            height: 300px;
            background-color: rgba(30, 86, 160, 0.05);
            animation: morphShape 15s linear infinite, float 10s ease-in-out infinite;
        }
        
        .contact-shape-2 {
            bottom: 10%;
            right: 5%;
            width: 250px;
            height: 250px;
            background-color: rgba(246, 185, 59, 0.05);
            animation: morphShape 20s linear infinite reverse, float 15s ease-in-out infinite;
        }
        
        @keyframes morphShape {
            0% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
            25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
            50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; }
            75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; }
            100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }
        
        /* Form success message */
        .form-success {
            display: none;
            background-color: rgba(40, 167, 69, 0.1);
            border-left: 4px solid #28a745;
            padding: 15px;
            border-radius: var(--border-radius-sm);
            margin-bottom: 20px;
            animation: fadeIn 0.5s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
