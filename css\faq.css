/* FAQ Page Specific Styles */
.faq-hero {
  /* background: linear-gradient(135deg, #2e7d32, #66bb6a); */
  background: linear-gradient(135deg, var(--accent-color), var(--success-color));

  color: #ffffff;
  padding: 100px 20px;
  text-align: center;
  margin-top: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}
  
.faq-hero::before {
  content: "";
  position: absolute;
  width: 400px;
  height: 400px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  top: -100px;
  left: -100px;
  z-index: 0;
}
  
.faq-hero::after {
  content: "";
  position: absolute;
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  bottom: -80px;
  right: -80px;
  z-index: 0;
}
  
.faq-hero .container {
  position: relative;
  z-index: 1;
  max-width: 900px;
  margin: 0 auto;
}
  
.faq-hero-content h1 {
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
  line-height: 1.2;
}
  
.faq-hero-content p {
  font-size: 1.2rem;
  color: #cfd8dc;
}
  
.faq-section {
  background-color: var(--light-color);
  position: relative;
  overflow: hidden;
}
  
.faq-container {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}
  
.faq-item {
  background: #fff;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  margin-bottom: 20px;
  overflow: hidden;
  transition: var(--transition);
  border-left: 4px solid var(--accent-color);
}
  
.faq-item:hover {
  box-shadow: var(--shadow);
  transform: translateY(-3px);
}
  
.faq-question {
  padding: 20px 30px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: var(--dark-color);
  position: relative;
}
  
.faq-question::after {
  content: "\f107";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  color: var(--primary-color);
  transition: var(--transition);
}
  
.faq-item.active .faq-question::after {
  transform: rotate(180deg);
}
  
.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease;
  padding: 0 30px;
}
  
.faq-item.active .faq-answer {
  max-height: 500px;
  padding: 0 30px 20px;
}
  
.faq-categories {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 10px;
}
  
.faq-category {
  padding: 10px 20px;
  background: #fff;
  border-radius: 30px;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  color: var(--dark-color);
  font-weight: 500;
}
  
.faq-category:hover,
.faq-category.active {
  background: var(--accent-color);
  color: #fff;
  box-shadow: var(--shadow);
}
  
.faq-search {
  margin-bottom: 40px;
  position: relative;
}
  
.faq-search input {
  width: 100%;
  padding: 15px 20px 15px 50px;
  border-radius: 30px;
  border: none;
  box-shadow: var(--shadow-sm);
  font-size: 16px;
  transition: var(--transition);
}
  
.faq-search input:focus {
  outline: none;
  box-shadow: var(--shadow);
}
  
.faq-search i {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
}
  
.shape-animation {
  position: absolute;
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  z-index: 0;
}
  
.faq-shape-1 {
  top: 10%;
  left: 5%;
  width: 300px;
  height: 300px;
  background-color: rgba(30, 86, 160, 0.05);
  animation: morphShape 15s linear infinite,
    float 10s ease-in-out infinite;
}
  
.faq-shape-2 {
  bottom: 10%;
  right: 5%;
  width: 250px;
  height: 250px;
  background-color: rgba(246, 185, 59, 0.05);
  animation: morphShape 20s linear infinite reverse,
    float 15s ease-in-out infinite;
}
  
@keyframes morphShape {
  0% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
  25% {
    border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
  }
  50% {
    border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
  }
  75% {
    border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
  }
  100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
}
  
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}
  
.contact-cta {
  text-align: center;
  margin-top: 60px;
  padding: 40px;
  background: #fff;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}
  
.contact-cta::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: var(--accent-color);
}
  
.contact-cta h3 {
  margin-bottom: 20px;
  color: var(--dark-color);
}
  
.contact-cta p {
  margin-bottom: 30px;
  color: var(--gray-color);
}
   