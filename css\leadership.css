.leadership-hero {
    background: linear-gradient(135deg, var(--accent-color), var(--success-color));
    color: #ffffff;
    padding: 85px 20px;
    text-align: center;
    margin-top: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.team-txt{
    color: var(--accent-color);
}
        .leadership-hero::before {
            content: "";
            position: absolute;
            width: 400px;
            height: 400px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            top: -100px;
            left: -100px;
            z-index: 0;
        }

        .leadership-hero::after {
            content: "";
  position: absolute;
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  bottom: -80px;
  right: -80px;
  z-index: 0;
        }

        .leadership-hero .container {
            position: relative;
  z-index: 1;
  max-width: 900px;
  margin: 0 auto;
        }

        .leadership-hero-content h1 {
            font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
  line-height: 1.2;
        }

        .leadership-hero-content p {
            font-size: 1.2rem;
            color: #cfd8dc;
          
        }

        .team-section {
            padding: 80px 0;
            position: relative;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin-top: 50px;
        }

        .team-member {
            background: #fff;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            position: relative;
        }

        .team-member:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-intense);
        }

        .team-photo {
            height: 280px;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .team-photo i {
            font-size: 6rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .team-photo::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, transparent 50%, rgba(0, 0, 0, 0.7) 100%);
            z-index: 1;
        }

        .team-info {
            padding: 30px;
            text-align: center;
        }

        .team-name {
            font-size: 1.5rem;
            color: var(--dark-color);
            margin-bottom: 5px;
            font-weight: 600;
        }

        .team-position {
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 15px;
            display: block;
        }

        .team-bio {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .team-social {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .team-social a {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--light-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--accent-color);
            transition: var(--transition);
        }

        .team-social a:hover {
            background: var(--accent-color);
            color: #fff;
            transform: translateY(-3px);
        }

        .philosophy-txt {
            color: var(--accent-color);
        }
        .philosophy-section {
            background-color: var(--light-color);
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }

        .philosophy-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .philosophy-text h2 {
            font-size: 2.5rem;
            color: var(--dark-color);
            margin-bottom: 25px;
            font-weight: 700;
            line-height: 1.2;
        }

        .philosophy-text p {
            color: #666;
            line-height: 1.8;
            margin-bottom: 20px;
            font-size: 1.1rem;
        }

        .philosophy-points {
            margin-top: 30px;
        }

        .philosophy-point {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .point-icon {
            width: 50px;
            height: 50px;
            background: var(--accent-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .point-text h4 {
            color: var(--dark-color);
            margin-bottom: 8px;
            font-size: 1.2rem;
        }

        .point-text p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 0;
        }

        .philosophy-image {
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .philosophy-illustration {
            width: 100%;
            height: 400px;
            background: var(--gradient-primary);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .philosophy-illustration i {
            font-size: 8rem;
            color: rgba(255, 255, 255, 0.3);
        }

        .philosophy-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--accent-color);
            color: #fff;
            padding: 10px 20px;
            border-radius: 30px;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(246, 185, 59, 0.3);
            z-index: 2;
        }

        .achievements-section {
            padding: 80px 0;
        }

        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            margin-top: 50px;
        }

        .achievement-card {
            background: #fff;
            border-radius: var(--border-radius);
            padding: 30px;
            text-align: center;
            box-shadow: var(--shadow);
            transition: var(--transition);
        }

        .achievement-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-intense);
        }

        .achievement-icon {
            width: 70px;
            height: 70px;
            background: var(--light-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: var(--accent-color);
            font-size: 1.8rem;
        }

        .achievement-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
            line-height: 1;
        }

        .achievement-text {
            color: var(--dark-color);
            font-weight: 500;
        }

        @media (max-width: 1200px) {
            .achievements-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 992px) {
            .team-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            .philosophy-content {
                grid-template-columns: 1fr;
                gap: 40px;
            }
            .philosophy-image {
                order: -1;
            }
        }

        @media (max-width: 768px) {
            .team-grid {
                grid-template-columns: 1fr;
                max-width: 400px;
                margin-left: auto;
                margin-right: auto;
            }
            .achievements-grid {
                grid-template-columns: 1fr;
                max-width: 400px;
                margin-left: auto;
                margin-right: auto;
            }
        }
