.story-hero {
    background: linear-gradient(135deg, var(--accent-color), var(--success-color));
    color: #ffffff;
    padding: 85px 20px;
    text-align: center;
    /* margin-top: 100px; */
    margin-top: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
      }

      .story-hero::before {
        content: "";
            position: absolute;
            width: 400px;
            height: 400px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            top: -100px;
            left: -100px;
            z-index: 0;
      }

      .story-hero::after {
        content: "";
  position: absolute;
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  bottom: -80px;
  right: -80px;
  z-index: 0;
      }

      .story-hero .container {
        position: relative;
  z-index: 1;
  max-width: 900px;
  margin: 0 auto;
      }
      .time-txt{
        color: var(--accent-color);
      }

      .story-hero-content h1 {
        font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
  line-height: 1.2;
      }

      .story-hero-content p {
        font-size: 1.2rem;
        color: #cfd8dc;
      }

      .timeline-section {
        padding: 80px 0;
        position: relative;
      }

      .timeline {
        position: relative;
        max-width: 1000px;
        margin: 0 auto;
        padding: 40px 0;
      }

      .timeline::before {
        content: "";
        position: absolute;
        width: 4px;
        background: var(--primary-light);
        top: 0;
        bottom: 0;
        left: 50%;
        margin-left: -2px;
        border-radius: 4px;
        opacity: 0.3;
      }

      .timeline-item {
        padding: 20px 40px;
        position: relative;
        width: 50%;
        box-sizing: border-box;
        margin-bottom: 40px;
      }

      .timeline-item:nth-child(odd) {
        left: 0;
      }

      .timeline-item:nth-child(even) {
        left: 50%;
      }

      .timeline-item::after {
        content: "";
        position: absolute;
        width: 20px;
        height: 20px;
        background: var(--primary-color);
        border-radius: 50%;
        top: 30px;
        z-index: 1;
        box-shadow: 0 0 0 4px rgba(30, 86, 160, 0.2);
      }

      .timeline-item:nth-child(odd)::after {
        right: -10px;
      }

      .timeline-item:nth-child(even)::after {
        left: -10px;
      }

      .timeline-content {
        background: #fff;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--shadow);
        position: relative;
        transition: var(--transition);
      }

      .timeline-content:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-intense);
      }

      .timeline-year {
        display: inline-block;
        background: var(--accent-color);
        color: #fff;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        margin-bottom: 15px;
        font-size: 0.9rem;
      }

      .timeline-content h3 {
        color: var(--dark-color);
        margin-bottom: 15px;
        font-size: 1.5rem;
      }

      .timeline-content p {
        color: #666;
        line-height: 1.6;
      }

      .values-section {
        background-color: var(--light-color);
        padding: 80px 0;
      }

      .values-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
        margin-top: 50px;
      }

      .value-card {
        background: #fff;
        border-radius: var(--border-radius);
        padding: 40px 30px;
        text-align: center;
        box-shadow: var(--shadow);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
      }

      .value-card:hover {
        transform: translateY(-10px);
        box-shadow: var(--shadow-intense);
      }

      .value-icon {
        width: 70px;
        height: 70px;
        background: var(--accent-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 25px;
        color: #fff;
        font-size: 1.8rem;
        position: relative;
      }

      .value-icon::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: var(--gradient-primary);
        opacity: 0.3;
        z-index: -1;
        animation: pulse 2s infinite;
      }

      .value-card h3 {
        color: var(--dark-color);
        margin-bottom: 15px;
        font-size: 1.5rem;
      }

      .value-card p {
        color: #666;
        line-height: 1.6;
      }

      @keyframes pulse {
        0% {
          transform: scale(1);
          opacity: 0.3;
        }
        50% {
          transform: scale(1.2);
          opacity: 0.1;
        }
        100% {
          transform: scale(1);
          opacity: 0.3;
        }
      }

      @media (max-width: 992px) {
        .values-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media (max-width: 768px) {
        .timeline::before {
          left: 40px;
        }
        .timeline-item {
          width: 100%;
          padding-left: 70px;
          padding-right: 20px;
        }
        .timeline-item:nth-child(odd),
        .timeline-item:nth-child(even) {
          left: 0;
        }
        .timeline-item:nth-child(odd)::after,
        .timeline-item:nth-child(even)::after {
          left: 30px;
        }
        .values-grid {
          grid-template-columns: 1fr;
        }
      }