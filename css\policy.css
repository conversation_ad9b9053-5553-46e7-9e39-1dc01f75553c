/* Policy Pages Styles */

/* Hero Section */
.policy-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: #fff;
    padding: 120px 0 80px;
    margin-top: 80px;
    position: relative;
    overflow: hidden;
}

.policy-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDQwIDQwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYxMCIgZmlsbC1vcGFjaXR5PSIwLjA1Ij48cGF0aCBkPSJNMCAwaDQwdjQwSDB6TTE4IDIwaDJ2MmgtMnptMCAwaC0ydi0yaDF2MWgxem0yIDBoMnYtMmgtMXYxaC0xem0yLTJoMnYtMmgtMnpNMjAgMTh2LTJoLTJ2MnptLTItMmgtMnYyaDJ6TTE2IDE2di0yaDJ2LTJoLTJ2MmgtMnYyaDJ6TTE0IDI0djJoMnYtMmgtMnptMi0ydjJoMnYtMmgtMnptMiAwaDJ2LTJoLTJ2MnptMi0yaDJ2LTJoLTJ2MnptMiAwdjJoMnYtMmgtMnptMiAydjJoMnYtMmgtMnptMiAwaDJ2LTJoLTJ2MnptMi00aDJ2LTJoLTJ2MnptLTIgMGgydi0yaC0ydjJ6Ii8+PC9nPjwvZz48L3N2Zz4=');
    opacity: 0.1;
    z-index: 0;
}

.policy-hero-content {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.policy-hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.policy-hero-content h1 i {
    color: var(--accent-color);
    font-size: 2.5rem;
}

.policy-hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
    line-height: 1.6;
}

.policy-meta {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.policy-meta span {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
    opacity: 0.8;
}

.policy-meta i {
    color: var(--accent-color);
}

/* Policy Content Layout */
.policy-content {
    padding: 80px 0;
    background: var(--light-color);
}

.policy-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 40px;
    align-items: start;
}

/* Sidebar */
.policy-sidebar {
    position: sticky;
    top: 120px;
}

.toc-card {
    background: #fff;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--shadow);
    border-left: 4px solid var(--accent-color);
}

.toc-card h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.toc-list {
    list-style: none;
}

.toc-list li {
    margin-bottom: 8px;
}

.toc-list a {
    color: var(--dark-color);
    text-decoration: none;
    padding: 8px 12px;
    border-radius: var(--border-radius-sm);
    display: block;
    transition: var(--transition);
    font-size: 0.95rem;
}

.toc-list a:hover {
    background: var(--light-color);
    color: var(--primary-color);
    padding-left: 20px;
}

/* Main Content */
.policy-main {
    background: #fff;
    border-radius: var(--border-radius);
    padding: 40px;
    box-shadow: var(--shadow);
}

.policy-section {
    margin-bottom: 50px;
    padding-bottom: 30px;
    border-bottom: 2px solid var(--light-color);
}

.policy-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.policy-section h2 {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
}

.policy-section h2 i {
    color: var(--accent-color);
    font-size: 1.5rem;
}

.policy-section h3 {
    color: var(--dark-color);
    font-size: 1.3rem;
    margin: 25px 0 15px 0;
    font-weight: 600;
}

.policy-section p {
    color: var(--gray-color);
    line-height: 1.7;
    margin-bottom: 15px;
    font-size: 1rem;
}

.policy-list {
    list-style: none;
    margin: 20px 0;
}

.policy-list li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 12px;
    color: var(--gray-color);
    line-height: 1.6;
}

.policy-list li::before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-size: 0.9rem;
}

/* Highlight Boxes */
.highlight-box, .warning-box {
    background: rgba(246, 185, 59, 0.1);
    border: 1px solid var(--accent-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin: 25px 0;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.warning-box {
    background: rgba(220, 53, 69, 0.1);
    border-color: var(--danger-color);
}

.highlight-box i, .warning-box i {
    color: var(--accent-color);
    font-size: 1.2rem;
    margin-top: 2px;
    flex-shrink: 0;
}

.warning-box i {
    color: var(--danger-color);
}

.highlight-box p, .warning-box p {
    margin: 0;
    color: var(--dark-color);
}

/* Use Cases Grid */
.use-cases {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin: 25px 0;
}

.use-case-card {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    transition: var(--transition);
    border: 2px solid transparent;
}

.use-case-card:hover {
    border-color: var(--accent-color);
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

.use-case-card i {
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 15px;
}

.use-case-card h4 {
    color: var(--dark-color);
    font-size: 1.1rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.use-case-card p {
    font-size: 0.9rem;
    margin: 0;
}

/* Sharing Grid */
.sharing-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin: 25px 0;
}

.sharing-item {
    background: #fff;
    border: 2px solid var(--light-color);
    border-radius: var(--border-radius);
    padding: 20px;
    transition: var(--transition);
}

.sharing-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.sharing-item i {
    color: var(--accent-color);
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.sharing-item h4 {
    color: var(--dark-color);
    font-size: 1.1rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.sharing-item p {
    font-size: 0.9rem;
    margin: 0;
}

/* Security Measures */
.security-measures {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin: 25px 0;
}

.security-item {
    display: flex;
    align-items: center;
    gap: 12px;
    background: var(--light-color);
    padding: 15px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.security-item:hover {
    background: #fff;
    box-shadow: var(--shadow-sm);
}

.security-item i {
    color: var(--primary-color);
    font-size: 1.1rem;
    flex-shrink: 0;
}

.security-item span {
    color: var(--dark-color);
    font-size: 0.95rem;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .policy-layout {
        grid-template-columns: 250px 1fr;
        gap: 30px;
    }
    
    .policy-main {
        padding: 30px;
    }
}

@media (max-width: 768px) {
    .policy-hero {
        padding: 100px 0 60px;
    }
    
    .policy-hero-content h1 {
        font-size: 2.2rem;
        flex-direction: column;
        gap: 10px;
    }
    
    .policy-meta {
        flex-direction: column;
        gap: 15px;
    }
    
    .policy-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .policy-sidebar {
        position: static;
        order: 2;
    }
    
    .policy-main {
        padding: 25px;
        order: 1;
    }
    
    .use-cases, .sharing-grid {
        grid-template-columns: 1fr;
    }
    
    .security-measures {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .policy-hero-content h1 {
        font-size: 1.8rem;
    }
    
    .policy-main {
        padding: 20px;
    }
    
    .policy-section h2 {
        font-size: 1.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}
