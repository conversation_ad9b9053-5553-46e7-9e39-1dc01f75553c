/* Renew Policy Page Styles */

/* General Styles */
:root {
    --primary-color: #2F80ED;
    --primary-dark: #1B62B3;
    --primary-light: #EBF3FF;
    --secondary-color: #27AE60;
    --text-color: #333333;
    --text-light: #666666;
    --text-lighter: #888888;
    --border-color: #E0E7FF;
    --success-color: #27AE60;
    --error-color: #EB5757;
    --warning-color: #F2C94C;
    --white: #ffffff;
    --off-white: #F8FAFF;
    --grey-light: #F0F4FF;
    --grey: #E0E7FF;
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;
    --gradient-primary: linear-gradient(135deg, #1B62B3 0%, #4A8EDA 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 15px;
}

p {
    margin-bottom: 15px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

.section-title {
    font-size: 32px;
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    padding-bottom: 15px;
}

.section-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: var(--gradient-primary);
}



.section-subtitle {
    text-align: center;
    color: var(--text-light);
    font-size: 18px;
    margin-bottom: 30px;
}

/* Specific button styles override for renew-policy page */
.hero-section .btn-primary,
.form-actions .btn-primary,
/* .cta-buttons .btn-primary {
    padding: 12px 24px;
    border-radius: 4px;
    font-weight: 500;
} */


.renew-btn {
    background: var(--dark-color) !important;
    padding: 1.2rem;
    border-radius: 1rem;
    color: #fff;
}

.renew-btn:hover {
    background-color: #041d2e !important;
    color: #ffd;
}
.hero-section .btn-secondary {
    padding: 12px 24px;
    border-radius: 4px;
    background-color: transparent ;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

/* Hero Section */
.hero-section {
    padding: 120px 0 100px;
    background: linear-gradient(135deg, #f0f7ff 0%, #e5eeff 100%);
    position: relative;
    overflow: hidden;
    margin-top: 0;
}

.hero-bg-shape {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: #fff;
    clip-path: polygon(0 70%, 100% 0, 100% 100%, 0% 100%);
    z-index: 1;
}

.hero-content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto 40px;
    position: relative;
    text-align: center;
}

.hero-badge {
    display: inline-block;
    /* background: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-dark) 100%); */
    background: var(--dark-color);
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(47, 128, 237, 0.3);
    position: relative;
    overflow: hidden;
    margin-top: 2.9rem;
}

.hero-badge::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(45deg);
    animation: shimmer 3s infinite linear;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
}

.hero-content h1 {
    font-size: 56px;
    font-weight: 700;
    margin-bottom: 25px;
    color: var(--text-color);
    line-height: 1.2;
    position: relative;
}

.gradient-text {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline;
    position: relative;
}

.hero-content p {
    font-size: 20px;
    margin-bottom: 30px;
    color: var(--text-light);
    line-height: 1.7;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    justify-content: center;
}

.btn-large {
    padding: 1.5rem;
    background: var(--accent-color) !important;
}

.hero-section .btn-primary {
    padding: 16px 32px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 16px;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--dark-color) 100%);
    color: white;
    border: none;
    box-shadow: 0 8px 20px rgba(47, 128, 237, 0.3);
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.hero-section .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    transition: all 0.4s ease;
    z-index: -1;
}

.hero-section .btn-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(47, 128, 237, 0.4);
}

.hero-section .btn-primary:hover::before {
    left: 0;
}

.hero-section .btn-secondary {
    padding: 16px 32px;
    /* border-radius: 50px; */
    font-weight: 600;
    font-size: 16px;
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.4s ease;
    position: relative;
    z-index: 1;
}

.hero-section .btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    /* background: rgba(47, 128, 237, 0.1); */
    transition: all 0.4s ease;
    z-index: -1;
    border-radius: 50px;
}

.hero-section .btn-secondary:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(47, 128, 237, 0.15);
}

.hero-section .btn-secondary:hover::before {
    width: 100%;
}

.hero-features {
    display: flex;
    gap: 25px;
    flex-wrap: wrap;
    justify-content: center;
}

.hero-feature {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    color: var(--text-light);
    background-color: rgba(255, 255, 255, 0.7);
    padding: 8px 16px;
    border-radius: 50px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.hero-feature:hover {
    transform: translateY(-3px);
    background-color: white;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.hero-feature i {
    color: var(--accent-color);
    font-size: 18px;
}

.hero-image {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-container {
    position: relative;
    z-index: 2;
    width: 100%;
    display: flex;
    justify-content: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    animation: float 6s ease-in-out infinite;
    filter: drop-shadow(0 20px 30px rgba(47, 128, 237, 0.2));
}

.hero-shape-1 {
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(47, 128, 237, 0.1) 0%, rgba(47, 128, 237, 0.05) 100%);
    top: -50px;
    right: -50px;
    z-index: 1;
}

.hero-shape-2 {
    position: absolute;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(47, 128, 237, 0.08) 0%, rgba(47, 128, 237, 0.03) 100%);
    bottom: -30px;
    left: -30px;
    z-index: 1;
}

@keyframes float {
    0% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(2deg);
    }
    100% {
        transform: translateY(0px) rotate(0deg);
    }
}

/* Fix for media queries */
@media (max-width: 992px) {
    .hero-section .container {
        flex-direction: column;
        text-align: center;
    }
    
    .hero-content {
        padding-right: 0;
        margin-bottom: 40px;
    }
    
    .hero-content h1 {
        font-size: 42px;
    }
    
    .hero-content p {
        font-size: 18px;
    }
    
    .hero-image {
        margin-top: 30px;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .process-steps {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
    
    .step-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .step-icon {
        margin: 0 auto;
    }
    
    .step-content {
        padding: 20px 15px;
    }
    
    .step-content p {
        font-size: 14px;
    }
    
    .step-features li {
        font-size: 13px;
    }
    
    .process-connector {
        width: 3px;
        height: 40px;
        margin: 10px 0;
    }
    
    .process-connector:before, .process-connector:after {
        left: 50%;
        transform: translate(-50%, 0);
    }
    
    .process-connector:before {
        top: 0;
    }
    
    .process-connector:after {
        top: auto;
        bottom: 0;
    }
    
    .stats-container {
        padding: 0 20px;
    }
    
    .stat-item {
        min-width: 280px;
        margin: 10px;
    }
    
    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-bottom-links {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: 80px 0 70px;
    }
    
    .hero-content h1 {
        font-size: 36px;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 15px;
    }
    
    .hero-features {
        gap: 15px;
    }
    
    .process-steps {
        grid-template-columns: 1fr;
        max-width: 500px;
        margin: 0 auto;
    }
    
    .step-header {
        flex-direction: row;
        text-align: left;
    }
}

@media (max-width: 576px) {
    .section-title {
        font-size: 28px;
    }
    
    .form-tabs {
        flex-direction: column;
    }
    
    .form-tab {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }
    
    .form-tab:last-child {
        border-bottom: none;
    }
    
    .tab-content {
        padding: 20px 15px;
    }
    
    .input-group {
        flex-direction: column;
    }
}

/* Benefits Section */
.benefits-section {
    padding: 100px 0;
    background-color: var(--off-white);
    position: relative;
    overflow: hidden;
}

.section-header {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 50px;
}

.section-badge {
    display: inline-block;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--dark-color) 100%);
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 6px 14px;
    border-radius: 50px;
    margin-bottom: 15px;
    box-shadow: 0 4px 12px rgba(47, 128, 237, 0.2);
}

.section-subtitle {
    color: var(--text-light);
    font-size: 18px;
    margin-top: 15px;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
    margin: 0 auto;
    max-width: 1200px;
    position: relative;
    z-index: 2;
}

.benefit-card {
    background-color: var(--white);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.4s ease;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.06);
    height: 100%;
}

.benefit-card:hover {
    transform: translateY(-10px);
    /* box-shadow: 0 15px 35px rgba(47, 128, 237, 0.15); */
    border: 2px solid rgb(219, 159, 48);
}

.benefit-card-inner {
    padding: 40px 30px;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.benefit-card.featured {
    border: 2px solid var(--accent-color);
    box-shadow: 0 15px 35px rgba(47, 128, 237, 0.1);
}

.benefit-tag {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 4px 12px;
    border-radius: 50px;
}

.benefit-icon {
    width: 75px;
    height: 75px;
    background: linear-gradient(135deg, rgba(47, 128, 237, 0.1) 0%, rgba(47, 128, 237, 0.05) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-bottom: 25px;
    position: relative;
    z-index: 1;
    transition: all 0.4s ease;
}

.benefit-icon::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(47, 128, 237, 0.15) 0%, rgba(47, 128, 237, 0.05) 100%);
    z-index: -1;
    transform: scale(0.8);
    opacity: 0.5;
    transition: transform 0.4s ease, opacity 0.4s ease;
}

.benefit-card:hover .benefit-icon {
    transform: scale(1.1);
    background: linear-gradient(135deg, rgba(47, 128, 237, 0.15) 0%, rgba(47, 128, 237, 0.1) 100%);
}

.benefit-card:hover .benefit-icon::before {
    transform: scale(1.2);
    opacity: 0.6;
}

.benefit-icon i {
    color: var(--accent-color);
    font-size: 30px;
    transition: transform 0.4s ease;
}

.benefit-card:hover .benefit-icon i {
    transform: scale(1.1);
}

.benefit-card h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: var(--text-color);
    transition: color 0.3s ease;
}

.benefit-card:hover h3 {
    color: var(--accent-color);
}

.benefit-card p {
    color: var(--text-light);
    font-size: 15px;
    line-height: 1.6;
    margin-bottom: 20px;
    flex-grow: 1;
}

.benefit-indicator {
    opacity: 0;
    margin-top: auto;
    text-align: right;
    transform: translateX(-10px);
    transition: all 0.4s ease;
}

.benefit-card:hover .benefit-indicator {
    opacity: 1;
    transform: translateX(0);
}

.benefit-indicator i {
    color: var(--primary-color);
    font-size: 18px;
    background-color: var(--primary-light);
    width: 30px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.benefit-card:hover .benefit-indicator i {
    background-color: var(--accent-color) ;
    color: white;
    box-shadow: 0 5px 15px rgba(47, 128, 237, 0.3);
}

.benefits-footer {
    text-align: center;
    margin-top: 50px;
}

.benefits-cta {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    /* background: linear-gradient(135deg, var(--accent-color) 0%, var(--dark-color) 100%); */
    background: var(--dark-color);
    color: white;
    font-size: 16px;
    font-weight: 600;
    padding: 14px 30px;
    border-radius: 50px;
    box-shadow: 0 8px 20px rgba(47, 128, 237, 0.3);
    transition: all 0.4s ease;
}

.benefits-cta:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(47, 128, 237, 0.4);
    color: white;
}

.benefits-cta i {
    transition: transform 0.3s ease;
}

.benefits-cta:hover i {
    transform: translateX(5px);
}

.benefits-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(47, 128, 237, 0.05) 0%, rgba(47, 128, 237, 0.02) 100%);
    z-index: 1;
}

.benefits-shape-1 {
    width: 400px;
    height: 400px;
    top: -150px;
    right: -150px;
}

.benefits-shape-2 {
    width: 300px;
    height: 300px;
    bottom: -100px;
    left: -100px;
}

.trust-txt{
    color: var(--accent-color)
}

/* Stats Section */
.stats-section {
    padding: 100px 0;
    /* background: linear-gradient(135deg, #2F80ED 0%, #1B62B3 100%); */
    background: var(--dark-color);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.stats-section .section-header {
    color: var(--white);
    margin-bottom: 60px;
}

.stats-section .section-title {
    color: var(--white);
}

.stats-section .section-title:after {
    background: rgba(255, 255, 255, 0.3);
}

.stats-section .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.stats-section .section-badge {
    background: rgba(255, 255, 255, 0.15);
    color: var(--white);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.stats-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    z-index: 1;
}

.stats-shape-1 {
    width: 400px;
    height: 400px;
    top: -150px;
    left: -150px;
}

.stats-shape-2 {
    width: 300px;
    height: 300px;
    bottom: -100px;
    right: -100px;
}

.stats-shape-3 {
    width: 200px;
    height: 200px;
    top: 30%;
    right: 10%;
    background: rgba(255, 255, 255, 0.03);
    animation: float 15s ease-in-out infinite alternate;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    position: relative;
    z-index: 2;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.4s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
    z-index: 2;
}

.stat-item:hover {
    transform: translateY(-15px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
    width: 80px;
    height: 80px;
    /* background: rgba(255, 255, 255, 0.15); */
    background:var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    transition: all 0.4s ease;
    position: relative;
}

.stat-icon::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px dashed rgba(255, 255, 255, 0.3);
    animation: spin 20s linear infinite;
}

.stat-item:hover .stat-icon {
    /* background: rgba(255, 255, 255, 0.25); */
    background:var(--accent-color);
    transform: scale(1.1);
}

.stat-icon i {
    font-size: 32px;
    color: white;
}

.stat-number {
    font-size: 54px;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 15px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
}

.stat-item:hover .stat-number {
    transform: scale(1.1);
}

.stat-symbol {
    font-size: 24px;
    margin-left: 5px;
    font-weight: 600;
    opacity: 0.9;
}

.stat-label {
    font-size: 18px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 15px;
}

.stat-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 15px;
    line-height: 1.6;
}

.stats-footer {
    margin-top: 60px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.trusted-by {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.trusted-by span {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 20px;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.trusted-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    flex-wrap: wrap;
}

.trusted-logos img {
    height: 40px;
    opacity: 0.7;
    filter: brightness(0) invert(1);
    transition: all 0.3s ease;
}

.trusted-logos img:hover {
    opacity: 1;
    transform: scale(1.1);
}

@media (max-width: 1200px) {
    .stats-container {
        gap: 30px;
    }
    
    .stat-number {
        font-size: 48px;
    }
    
    .stat-icon {
        width: 70px;
        height: 70px;
    }
}

@media (max-width: 768px) {
    .stats-section {
        padding: 80px 0;
    }
    
    .stats-container {
        grid-template-columns: 1fr;
        max-width: 450px;
        margin: 0 auto;
    }
    
    .trusted-logos {
        gap: 20px;
    }
    
    .trusted-logos img {
        height: 30px;
    }
}

/* Renewal Process Section */
.renewal-process-section {
    padding: 100px 0;
    background-color: #f8faff;
    position: relative;
    overflow: hidden;
}

.renewal-process-section .section-header {
    margin-bottom: 60px;
}

.process-container {
    position: relative;
    z-index: 5;
    max-width: 1200px;
    margin: 0 auto;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    position: relative;
    z-index: 2;
}

.process-step {
    background-color: var(--white);
    border-radius: 15px;
    padding: 0;
    box-shadow: 0 5px 20px rgba(47, 128, 237, 0.06);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    border: none;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.process-step:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(47, 128, 237, 0.1);
}

.step-header {
    background: #f0f7ff;
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    text-align: center;
}

.step-icon {
    width: 80px;
    height: 80px;
    background: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(47, 128, 237, 0.1);
    position: relative;
    z-index: 1;
    transition: all 0.4s ease;
    border: none;
    margin: 0 auto;
}

.step-number {
    position: absolute;
    right: 30px;
    top: 30px;
    font-size: 24px;
    font-weight: 500;
    color: rgba(47, 128, 237, 0.5);
}

.step-icon i {
    font-size: 30px;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.step-content {
    padding: 30px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
}

.step-content h3 {
    font-size: 22px;
    margin-bottom: 15px;
    color: var(--text-color);
    transition: color 0.3s ease;
}

.step-content p {
    color: var(--text-light);
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 20px;
}

.step-features {
    list-style: none;
    padding: 0;
    margin: 20px 0 0;
    flex-grow: 1;
}

.step-features li {
    padding: 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-light);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.step-features li:last-child {
    border-bottom: none;
}

.step-features li i {
    color: var(--primary-color);
    font-size: 14px;
}

.process-visual {
    position: relative;
    margin-top: 60px;
    display: flex;
    justify-content: center;
}

.process-timeline {
    position: relative;
    width: 50%;
    max-width: 600px;
    margin: 0 auto;
    padding: 0;
}

.timeline-track {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: #cee0ff;
    transform: translateY(-50%);
}

.timeline-dot {
    width: 12px;
    height: 12px;
    background-color: var(--white);
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    transition: all 0.3s ease;
}

.timeline-dot[data-step="1"] {
    left: 0%;
    background-color: var(--primary-color);
}

.timeline-dot[data-step="2"] {
    left: 50%;
    margin-left: -6px;
}

.timeline-dot[data-step="3"] {
    right: 0%;
}

.process-note {
    margin-top: 15px;
    font-size: 14px;
    color: var(--text-light);
    font-style: italic;
}

.process-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(47, 128, 237, 0.03) 0%, rgba(47, 128, 237, 0.01) 100%);
    z-index: 1;
}

.process-shape-1 {
    width: 500px;
    height: 500px;
    top: -200px;
    right: -200px;
}

.process-shape-2 {
    width: 400px;
    height: 400px;
    bottom: -150px;
    left: -150px;
}

@media (max-width: 992px) {
    .process-steps {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
    
    .step-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .step-icon {
        margin: 0 auto;
    }
    
    .step-content {
        padding: 20px 15px;
    }
    
    .step-content p {
        font-size: 14px;
    }
    
    .step-features li {
        font-size: 13px;
    }
}

@media (max-width: 768px) {
    .renewal-process-section {
        padding: 70px 0;
    }
    
    .process-steps {
        grid-template-columns: 1fr;
        max-width: 500px;
        margin: 0 auto;
    }
    
    .step-header {
        flex-direction: row;
        text-align: left;
    }
}

/* Renewal Form Section */
.renewal-form-section {
    padding: 80px 0;
    background-color: var(--white);
}

.form-container {
    max-width: 700px;
    margin: 0 auto;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: var(--shadow);
    overflow: hidden;
    border: 1px solid var(--grey-light);
}

.form-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.form-tab {
    flex: 1;
    text-align: center;
    padding: 15px;
    cursor: pointer;
    background-color: var(--grey-light);
    transition: var(--transition);
    border-right: 1px solid var(--border-color);
    font-weight: 500;
}

.form-tab:last-child {
    border-right: none;
}

.form-tab.active {
    background-color: var(--white);
    color: var(--accent-color);
    border-bottom: 3px solid var(--accent-color);
}

.tab-content {
    padding: 30px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    transition: var(--transition);
    font-size: 16px;
}

.form-group input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px var(--primary-light);
}

.input-group {
    display: flex;
    gap: 10px;
}

.input-group input {
    flex: 1;
}

.form-actions {
    margin-top: 30px;
    text-align: center;
}

.form-actions button {
    min-width: 200px;
}

/* Error/Success Messages */
.error-message {
    color: var(--error-color);
    font-size: 14px;
    margin-top: 5px;
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    text-align: center;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background-color: var(--off-white);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background-color: var(--white);
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: var(--shadow);
    overflow: hidden;
    border: 1px solid transparent;
    transition: var(--transition);
}

.faq-item:hover {
    border-color: var(--primary-light);
}

.faq-question {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: var(--transition);
}

.faq-question:hover {
    background-color: var(--grey-light);
}

.faq-question h3 {
    font-size: 18px;
    margin-bottom: 0;
}

.faq-icon i {
    transition: var(--transition);
    color: var(--primary-color);
}

.faq-item.active .faq-icon i {
    transform: rotate(180deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-item.active .faq-answer {
    max-height: 500px;
}

.faq-answer p {
    padding: 0 20px 20px;
    color: var(--text-light);
}

/* Contact Support Section */
.contact-support-section {
    padding: 80px 0;
    background-color: var(--white);
}

.support-options {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 40px;
}

.support-option {
    flex: 1;
    min-width: 300px;
    text-align: center;
    padding: 30px 20px;
    margin: 0 15px 30px;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid transparent;
}

.support-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-light);
}

.support-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto 20px;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(27, 98, 179, 0.15);
}

.support-option:hover .support-icon {
    transform: scale(1.1);
}

.support-icon i {
    color: var(--accent-color);
    font-size: 28px;
}

.support-option h3 {
    font-size: 20px;
    margin-bottom: 10px;
}

.support-option p {
    color: var(--text-light);
    margin-bottom: 15px;
}

.support-link {
    display: block;
    font-weight: 500;
    margin-bottom: 10px;
}

.support-timing {
    font-size: 14px;
    color: var(--text-lighter);
}

/* CTA Section */
.cta-section {
    padding: 60px 0;
    background: var(--gradient-primary);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    top: -150px;
    left: -150px;
}

.cta-section::after {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    bottom: -100px;
    right: -100px;
}

.cta-content {
    text-align: center;
    max-width: 700px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.cta-content h2 {
    font-size: 32px;
    margin-bottom: 15px;
}

.cta-content p {
    margin-bottom: 30px;
    font-size: 18px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.cta-buttons .btn-primary {
    background-color: var(--white);
    color: var(--primary-color);
}

.cta-buttons .btn-primary:hover {
    background-color: var(--accent-color);
}

.cta-buttons .btn-secondary {
    background-color: transparent;
    color: var(--white);
    border-color: var(--white);
}

.cta-buttons .btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Footer */
.site-footer {
    background-color: #222;
    color: var(--white);
    padding-top: 60px;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 30px;
    margin-bottom: 40px;
}

.footer-column {
    flex: 1;
    min-width: 250px;
}

/* .footer-logo img {
    height: 50px;
    margin-bottom: 20px;
}

.footer-logo-img {
    filter: brightness(0) invert(1);
} */

.footer-column p {
    color: #bbb;
    font-size: 14px;
    margin-bottom: 20px;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--white);
    transition: var(--transition);
}

.social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

.footer-column h3 {
    font-size: 18px;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-column h3:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--gradient-primary);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #bbb;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--white);
    padding-left: 5px;
}

.contact-info {
    list-style: none;
}

.contact-info li {
    margin-bottom: 15px;
    color: #bbb;
    display: flex;
    align-items: flex-start;
}

.contact-info li i {
    margin-right: 10px;
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.copyright {
    font-size: 14px;
    color: #bbb;
}

.footer-bottom-links {
    display: flex;
    gap: 20px;
}

.footer-bottom-links a {
    color: #bbb;
    font-size: 14px;
    transition: var(--transition);
}

.footer-bottom-links a:hover {
    color: var(--white);
}

/* Responsive Styles */
@media (max-width: 576px) {
    .section-title {
        font-size: 28px;
    }
    
    .form-tabs {
        flex-direction: column;
    }
    
    .form-tab {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }
    
    .form-tab:last-child {
        border-bottom: none;
    }
    
    .tab-content {
        padding: 20px 15px;
    }
    
    .input-group {
        flex-direction: column;
    }
}

/* Add these rules for better buttons */
.form-actions .btn-primary,
.hero-buttons .btn-primary,
.cta-buttons .btn-primary {
    background: var(--accent-color);
    color: var(--white);
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    display: inline-block;
}

.form-actions .btn-primary:hover,
.hero-buttons .btn-primary:hover {
    box-shadow: 0 10px 20px rgba(27, 98, 179, 0.2);
    transform: translateY(-3px);
}

.hero-buttons .btn-secondary {
    /* background-color: var(--white); */
    color: var(--accent-color);
    border: 1px solid var(--accent-color);
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: var(--transition);
}

.hero-buttons .btn-secondary:hover {
    background-color: var(--primary-light);
    transform: translateY(-3px);
}

.btn-otp {
    background: var(--dark-color);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.policy-txt{
    color: var(--accent-color);
}

.btn-otp:hover {
    box-shadow: 0 5px 15px rgba(27, 98, 179, 0.2);
}

.trust-indicators {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 25px;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.trust-item i {
    color: var(--white);
    font-size: 16px;
}

/* Make CTA span subtitle match the site theme */
.cta-section .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 1.5px;
    display: block;
    margin-bottom: 10px;
}

.process-step.active {
    border-color: var(--primary-color);
    box-shadow: 0 15px 35px rgba(47, 128, 237, 0.15);
}

.process-step.pulse-animation {
    animation: pulse 1.5s ease;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(47, 128, 237, 0.4);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(47, 128, 237, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(47, 128, 237, 0);
    }
}

@media (max-width: 992px) {
    .process-timeline {
        width: 90%;
    }
}

@media (max-width: 768px) {
    .process-timeline {
        display: none;
    }
}

/* Add new styles for process cards */
.process-cards-container {
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    justify-content: center;
    margin: 60px 0;
}

.process-card {
    background: var(--white);
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    padding: 30px;
    flex: 1;
    min-width: 300px;
    max-width: 380px;
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid var(--grey);
    display: flex;
    flex-direction: column;
}

.process-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(47, 128, 237, 0.1);
    border-color: var(--accent-color);
}

.process-card-icon {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 25px;
}

.circle-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.process-card:hover .circle-icon {
    background: var(--accent-color);
}

.circle-icon i {
    font-size: 30px;
    color: var(--accent-color);
    transition: all 0.3s ease;
}

.process-card:hover .circle-icon i {
    color: var(--white);
}

.step-number {
    position: absolute;
    top: 0;
    right: 30px;
    width: 28px;
    height: 28px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 4px 10px rgba(47, 128, 237, 0.3);
}

.process-card-content {
    flex: 1;
}

.process-card-content h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
}

.process-card-content p {
    font-size: 15px;
    color: var(--text-light);
    margin-bottom: 20px;
    line-height: 1.6;
}

.process-card-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.process-card-features li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;
    font-size: 14px;
    color: var(--text-light);
}

.process-card-features li i {
    color: var(--secondary-color);
    margin-right: 10px;
    font-size: 16px;
}

/* Media queries for process cards */
@media (max-width: 992px) {
    .process-cards-container {
        flex-direction: column;
        align-items: center;
    }
    
    .process-card {
        max-width: 100%;
        width: 100%;
    }
}

@media (max-width: 576px) {
    .process-card {
        padding: 25px 20px;
    }
    
    .circle-icon {
        width: 70px;
        height: 70px;
    }
    
    .circle-icon i {
        font-size: 25px;
    }
    
    .step-number {
        right: 20px;
    }
} 