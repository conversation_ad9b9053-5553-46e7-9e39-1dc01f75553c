/* Retirement Plans - Modern UI Styles
   Created for Growwell Financial Services
-------------------------------------------------- */

:root {
  --primary-color: #0c2340;
  --secondary-color: #F6B93B;
  --accent-color: #f6b93b;
  --light-accent: #e8f3ff;
  --dark-accent: #0a4d8c;
  --text-color: #333333;
  --light-gray: #f5f7fa;
  --medium-gray: #e0e4eb;
  --dark-gray: #6c757d;
  --white: #ffffff;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 20px;
}

/* ===== Hero Section Enhancements ===== */
.retirement-hero {
  position: relative;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f3ff 100%);
  padding: 120px 0 100px;
  overflow: hidden;
}

.retirement-hero .container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
}

.retirement-txt{
  color: var(--accent-color);
}

.rt-image{
  border-radius: 2rem;
}

.hero-wrapper {
  display: grid;
  grid-template-columns: 1.2fr 0.8fr;
  align-items: center;
  gap: 20px;
}

.hero-left {
  flex: 1;
  max-width: 600px;
  z-index: 3;
}

.hero-right {
  flex: 1;
  position: relative;
  min-height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.decorative-circle {
  position: absolute;
  border-radius: 50%;
  display: none;
}

.circle-1 {
  top: 5%;
  right: 10%;
  width: 100px;
  height: 100px;
  background: rgba(65, 151, 231, 0.1);
  animation: none;
}

.circle-2 {
  bottom: 15%;
  left: 5%;
  width: 70px;
  height: 70px;
  background: rgba(255, 158, 27, 0.08);
  animation: none;
}

.circle-3 {
  top: 30%;
  left: 15%;
  width: 50px;
  height: 50px;
  background: rgba(65, 151, 231, 0.12);
  animation: none;
}

.floating-card {
  position: absolute;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  display: none;
}

.floating-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.floating-card-1 {
  bottom: 30%;
  left: 10%;
  animation: none;
}

.floating-card-2 {
  top: 20%;
  right: 15%;
  animation: none;
}

.floating-card-3 {
  top: 60%;
  right: 10%;
  animation: none;
}

.floating-card i {
  color: var(--accent-color);
  font-size: 20px;
  background: rgba(65, 151, 231, 0.1);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.floating-card:hover i {
  transform: rotate(10deg) scale(1.1);
  background: var(--accent-color);
  color: white;
}

.floating-card p {
  font-size: 15px;
  font-weight: 600;
  margin: 0;
  color: var(--primary-color);
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  background: var(--light-accent);
  color: var(--accent-color);
  font-weight: 600;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 30px;
  margin-bottom: 20px;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(65, 151, 231, 0.2);
}

.hero-badge i {
  margin-left: 6px;
  font-size: 14px;
}

.retirement-hero h1 {
  font-size: 48px;
  font-weight: 800;
  margin-bottom: 20px;
  color: var(--primary-color);
  line-height: 1.2;
}

.gradient-text {
  background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
}

.retirement-hero p {
  font-size: 18px;
  line-height: 1.7;
  color: var(--dark-gray);
  margin-bottom: 30px;
}

.hero-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 30px;
  padding: 1rem;
}

.hero-stat-item {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(5px);
  border-radius: var(--border-radius-md);
  padding: 12px;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(224, 228, 235, 0.7);
  transition: transform var(--transition-normal);
  min-width: 110px;
}

.hero-stat-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.hero-stat-item .stat-value {
  font-size: 22px;
  font-weight: 700;
  color: var(--primary-color);
  white-space: nowrap;
}

.hero-stat-item .stat-label {
  font-size: 13px;
  color: var(--dark-gray);
}

.retirement-hero-cta {
  display: flex;
  gap: 16px;
  margin-top: 32px;
}

.retirement-showcase {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 320px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.retirement-illustration {
  position: relative;
  width: 220px;
  height: 220px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  z-index: 2;
}

.illustration-icon {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--white);
  box-shadow: var(--shadow-md);
  z-index: 3;
}

.main-icon {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #4197e7, #0a4d8c);
  z-index: 4;
  box-shadow: 0 10px 30px rgba(65, 151, 231, 0.3);
  border: 6px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.main-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 15px 40px rgba(65, 151, 231, 0.4);
}

.main-icon i {
  font-size: 50px;
  color: var(--white);
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.1));
}

.icon-1, .icon-2, .icon-3 {
  width: 60px;
  height: 60px;
  animation: orbitAnimation 15s linear infinite;
  background: white;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  border: 4px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.icon-1 {
  animation-delay: 0s;
}

.icon-2 {
  animation-delay: -5s;
}

.icon-3 {
  animation-delay: -10s;
}

.icon-1 i, .icon-2 i, .icon-3 i {
  font-size: 24px;
  color: var(--accent-color);
  transition: all 0.3s ease;
}

.illustration-icon:hover {
  transform: scale(1.15);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.illustration-icon:hover i {
  transform: rotate(15deg);
  color: var(--primary-color);
}

.pulse-circle {
  position: absolute;
  width: 220px;
  height: 220px;
  border-radius: 50%;
  background: transparent;
  border: 2px dashed rgba(65, 151, 231, 0.3);
  animation: pulse 3s ease-in-out infinite;
}

.pulse-circle::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px dashed rgba(65, 151, 231, 0.15);
  animation: pulse-reverse 3s ease-in-out infinite;
}

.pulse-circle::after {
  content: '';
  position: absolute;
  width: 270px;
  height: 270px;
  top: -25px;
  left: -25px;
  border-radius: 50%;
  border: 2px dashed rgba(255, 158, 27, 0.2);
  animation: pulse 4s ease-in-out infinite 1s;
}

.sebi-badge {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: white;
  border-radius: 30px;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 700;
  color: var(--primary-color);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--medium-gray);
  transition: all 0.3s ease;
}

.sebi-badge:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.sebi-badge i {
  color: var(--secondary-color);
  font-size: 16px;
  animation: pulse 2s ease-in-out infinite;
}

.btn-hero {
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  border-radius: var(--border-radius-md);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
  gap: 10px;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background: var(--accent-color);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background: rgba(12, 35, 64, 0.05);
  transform: translateY(-3px);
}

.btn-hero i {
  font-size: 18px;
  transition: transform 0.3s ease;
  margin-right: 4px;
  position: relative;
}

.btn-hero:hover i {
  transform: translateX(5px);
}

.btn-plan {
  display: inline-flex;
  width: 100%;
  padding: 16px;
  background: var(--accent-color);
  color: var(--white);
  font-size: 16px;
  font-weight: 600;
  border-radius: var(--border-radius-md);
  justify-content: center;
  align-items: center;
  gap: 10px;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn-plan:hover {
  background: var(--accent-color);
  box-shadow: var(--shadow-lg);
  transform: translateY(-3px);
}

.btn-plan i {
  font-size: 18px;
  transition: transform 0.3s ease;
  position: relative;
}

.btn-plan:hover i {
  transform: translateX(6px);
}

.plan-card.popular .btn-plan {
  background: var(--secondary-color);
}

.plan-card.popular .btn-plan:hover {
  background: #e68c18;
}

/* Additional animations */
@keyframes orbitAnimation {
  0% {
    transform: rotate(0deg) translateX(120px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(120px) rotate(-360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.6;
  }
}

@keyframes pulse-reverse {
  0%, 100% {
    transform: scale(1.05);
    opacity: 0.6;
  }
  50% {
    transform: scale(1);
    opacity: 0.4;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

/* Add a gentle spotlight effect behind the main illustration */
.retirement-showcase::before {
  content: '';
  position: absolute;
  width: 350px;
  height: 350px;
  background: radial-gradient(circle, rgba(65, 151, 231, 0.12) 0%, rgba(65, 151, 231, 0) 70%);
  border-radius: 50%;
  z-index: 1;
}

.retirement-showcase::after {
  content: '';
  position: absolute;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255, 158, 27, 0.08) 0%, rgba(255, 158, 27, 0) 70%);
  border-radius: 50%;
  z-index: 1;
  top: 60%;
  left: 20%;
  animation: float 10s ease-in-out infinite 2s;
}

/* ===== Benefits Section Enhancements ===== */
.retirement-benefits {
  padding: 100px 0;
  background-color: var(--white);
  position: relative;
  overflow: hidden;
}

.retirement-benefits::before {
  content: '';
  position: absolute;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(65, 151, 231, 0.05) 0%, rgba(65, 151, 231, 0) 70%);
  border-radius: 50%;
  top: -200px;
  left: -200px;
  z-index: 0;
}

.retirement-benefits::after {
  content: '';
  position: absolute;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(255, 158, 27, 0.05) 0%, rgba(255, 158, 27, 0) 70%);
  border-radius: 50%;
  bottom: -150px;
  right: -150px;
  z-index: 0;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}

.section-subtitle {
  display: inline-block;
  font-size: 15px;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--accent-color);
  letter-spacing: 1.5px;
  margin-bottom: 15px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.section-description {
  font-size: 18px;
  line-height: 1.6;
  color: var(--dark-gray);
  max-width: 700px;
  margin: 0 auto;
}

.title-underline {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
  margin: 0 auto 25px;
  border-radius: 2px;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 30px;
  margin-top: 40px;
  position: relative;
  z-index: 2;
}

.benefit-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: 30px;
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  border: 1px solid var(--medium-gray);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.benefit-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.benefit-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(to bottom, var(--accent-color), var(--secondary-color));
  border-radius: 5px 0 0 5px;
  transition: width 0.3s ease;
}

.benefit-card:hover::before {
  width: 7px;
}

.benefit-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  border-radius: 20px;
  background: var(--light-accent);
  margin-bottom: 25px;
  position: relative;
  transition: all var(--transition-normal);
}

.benefit-card:hover .benefit-icon {
  transform: scale(1.1);
  background: linear-gradient(135deg, var(--light-accent), var(--light-accent));
  box-shadow: 0 10px 20px rgba(65, 151, 231, 0.15);
}

.benefit-icon i {
  font-size: 28px;
  color: var(--accent-color);
  transition: all var(--transition-normal);
}

.benefit-card:hover .benefit-icon i {
  color: var(--primary-color);
}

.benefit-title {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--primary-color);
  transition: color var(--transition-normal);
}

.benefit-card:hover .benefit-title {
  color: var(--accent-color);
}

.benefit-description {
  font-size: 16px;
  line-height: 1.6;
  color: var(--dark-gray);
  margin-bottom: 20px;
  flex-grow: 1;
}

.benefit-link {
  margin-top: auto;
}

.benefit-link a {
  display: inline-flex;
  align-items: center;
  color: var(--accent-color);
  font-weight: 600;
  text-decoration: none;
  transition: all var(--transition-normal);
}

.benefit-link a i {
  margin-left: 8px;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.benefit-link a:hover {
  color: var(--secondary-color);
}

.benefit-link a:hover i {
  transform: translateX(6px);
}

/* ===== Retirement Plans Section Enhancements ===== */
.retirement-plans {
  padding: 100px 0;
  background-color: var(--light-gray);
  position: relative;
  overflow: hidden;
}

.retirement-plans::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.plans-tabs {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.plan-tab {
  padding: 12px 24px;
  border-radius: 30px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  color: var(--dark-gray);
  background: var(--white);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--medium-gray);
}

.plan-tab.active {
  background: var(--accent-color);
  color: var(--white);
  box-shadow: 0 4px 12px rgba(65, 151, 231, 0.3);
  border-color: var(--accent-color);
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
}

.plan-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  position: relative;
  border: 1px solid var(--medium-gray);
}

.plan-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
}

.plan-card.popular {
  border: 2px solid var(--secondary-color);
  box-shadow: 0 10px 25px rgba(255, 158, 27, 0.15);
}

.plan-badge {
  position: absolute;
  top: 20px;
  right: 0;
  background: var(--secondary-color);
  color: var(--white);
  font-size: 14px;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 30px 0 0 30px;
  box-shadow: var(--shadow-sm);
  z-index: 2;
}

.plan-card.popular .plan-badge {
  background: var(--primary-color);
}

.plan-header {
  padding: 30px;
  text-align: center;
  background: var(--light-accent);
  border-bottom: 1px solid var(--medium-gray);
}

.plan-name {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.plan-description {
  font-size: 15px;
  color: var(--dark-gray);
  margin-bottom: 20px;
  line-height: 1.6;
}

.plan-price {
  font-size: 42px;
  font-weight: 800;
  color: var(--accent-color);
  line-height: 1;
  margin-bottom: 5px;
}

.plan-price sup {
  font-size: 24px;
  font-weight: 600;
  margin-left: 2px;
  top: -0.5em;
}

.plan-duration {
  font-size: 14px;
  color: var(--dark-gray);
  font-weight: 500;
}

.plan-features {
  padding: 30px;
  list-style: none;
}

.plan-features li {
  display: flex;
  align-items: center;
  font-size: 16px;
  margin-bottom: 16px;
  color: var(--text-color);
}

.plan-features li:last-child {
  margin-bottom: 0;
}

.plan-features i {
  color: var(--success-color);
  margin-right: 12px;
  font-size: 18px;
}

.plan-cta {
  padding: 0 30px 30px;
  text-align: center;
}

.btn-plan i {
  margin-left: 8px;
  font-size: 16px;
  transition: transform 0.3s ease;
}

.btn-plan:hover i {
  transform: translateX(4px);
}

/* ===== Calculator Section Enhancements ===== */
.retirement-calculator {
  padding: 100px 0;
  background: linear-gradient(135deg, #0c2340 0%, #0a4d8c 100%);
  color: var(--white);
  position: relative;
  overflow: hidden;
}

.retirement-calculator::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: 1;
}

.retirement-calculator::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(255, 158, 27, 0.1) 0%, rgba(255, 158, 27, 0) 70%);
  border-radius: 50%;
  z-index: 1;
}

.retirement-calculator .section-header h2 {
  color: var(--white);
}

.retirement-calculator .section-subtitle {
  color: var(--secondary-color);
}

.retirement-calculator .section-description {
  color: rgba(255, 255, 255, 0.8);
}

.calculator-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius-lg);
  padding: 40px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-lg);
  position: relative;
  z-index: 2;
}

.calculator-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 30px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.form-title {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--white);
  position: relative;
  padding-bottom: 15px;
}

.form-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--secondary-color);
  border-radius: 3px;
}

.calculator-group {
  margin-bottom: 5px;
}

.calculator-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
}

.calculator-input {
  width: 100%;
  padding: 14px;
  font-size: 16px;
  border-radius: var(--border-radius-md);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  transition: all var(--transition-fast);
}

.calculator-input:focus {
  border-color: var(--secondary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 158, 27, 0.3);
}

.slider-container {
  position: relative;
}

.calculator-slider {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  margin-bottom: 20px;
}

.calculator-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--secondary-color);
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all var(--transition-fast);
}

.calculator-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.slider-value {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.calculate-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--secondary-color);
  color: var(--white);
  border: none;
  padding: 15px;
  font-size: 16px;
  font-weight: 600;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  margin-top: 10px;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.calculate-btn:hover {
  background: #e68c18;
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.calculate-btn i {
  margin-right: 8px;
}

.calculator-result {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  padding: 30px;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.result-title {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 25px;
  text-align: center;
  color: var(--white);
  position: relative;
  padding-bottom: 15px;
}

.result-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--secondary-color);
  border-radius: 3px;
}

.result-chart {
  flex: 1;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--border-radius-md);
  margin-bottom: 25px;
  min-height: 200px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.5);
}

.chart-placeholder i {
  font-size: 40px;
  margin-bottom: 10px;
}

.chart-placeholder p {
  font-size: 14px;
}

.result-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.result-stat {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--border-radius-md);
  padding: 20px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: transform var(--transition-normal);
}

.result-stat:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.stat-value {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 5px;
  color: var(--white);
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.result-note {
  margin-top: 20px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.result-note i {
  color: var(--secondary-color);
  margin-right: 5px;
}

.calculator-benefits {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 40px;
  position: relative;
  z-index: 2;
}

.benefit-item {
  background: rgba(255, 255, 255, 0.08);
  padding: 25px;
  border-radius: var(--border-radius-lg);
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: transform var(--transition-normal);
}

.benefit-item:hover {
  transform: translateY(-5px);
}

.benefit-item .benefit-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 158, 27, 0.2);
  margin-bottom: 15px;
}

.benefit-item .benefit-icon i {
  font-size: 24px;
  color: var(--secondary-color);
}

.benefit-item h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--white);
}

.benefit-item p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

/* ===== Process Section Enhancements ===== */
.investment-process {
  padding: 120px 0 100px;
  background: linear-gradient(135deg, #f8faff 0%, #f0f5ff 100%);
  position: relative;
  overflow: hidden;
}

.investment-process::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('../images/investment-pattern.svg') no-repeat center;
  background-size: cover;
  opacity: 0.05;
  z-index: 1;
}

.investment-process .container {
  position: relative;
  z-index: 2;
}

.process-steps {
  max-width: 1200px;
  margin: 80px auto 0;
  position: relative;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

/* Remove the vertical line since we're using a grid layout now */
.process-steps:before {
  display: none;
}

.process-step {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.06);
  transition: all 0.4s ease;
  overflow: hidden;
  height: 100%;
  border: 2px solid transparent;
}

.process-step:hover {
  transform: translateY(-15px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
  border-color: #ff9e1b;
}

.process-step-number {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #2c86e0 0%, #64b5ff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 2rem;
  font-weight: 700;
  margin: -40px auto 20px;
  box-shadow: 0 15px 30px rgba(44, 134, 224, 0.3);
  position: relative;
  z-index: 3;
  transition: all 0.3s ease;
}

.process-step:hover .process-step-number {
  background: linear-gradient(135deg, #ff9e1b 0%, #ffb74d 100%);
  transform: scale(1.1) rotate(360deg);
  transition: all 0.5s ease;
}

.process-step-content {
  padding: 30px 35px 50px;
  text-align: center;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.process-step-title {
  font-size: 1.6rem;
  font-weight: 700;
  color: #0c2340;
  margin-bottom: 25px;
  position: relative;
  padding-bottom: 18px;
}

.process-step-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 4px;
  background: linear-gradient(90deg, #2c86e0, #64b5ff);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.process-step:hover .process-step-title::after {
  width: 100px;
  background: linear-gradient(90deg, #ff9e1b, #ffb74d);
}

.process-step-description {
  color: #647082;
  line-height: 1.8;
  font-size: 1.1rem;
}

.process-step-icon {
  margin-bottom: 25px;
  width: 100px;
  height: 100px;
  background: rgba(44, 134, 224, 0.1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease;
}

.process-step-icon i {
  font-size: 2.8rem;
  transition: all 0.4s ease;
}

.process-step:hover .process-step-icon {
  background: rgba(255, 158, 27, 0.1);
  transform: rotate(10deg);
}

.process-step:hover .process-step-icon i {
  transform: scale(1.2);
}

/* Process step specific icons */
.process-step:nth-child(1) .process-step-icon i {
  color: #2c86e0; /* Blue */
}

.process-step:nth-child(2) .process-step-icon i {
  color: #2cb67d; /* Green */
}

.process-step:nth-child(3) .process-step-icon i {
  color: #ff7849; /* Orange */
}

.process-step:nth-child(4) .process-step-icon i {
  color: #7e57c2; /* Purple */
}

.process-step:nth-child(5) .process-step-icon i {
  color: #ff5e7d; /* Pink */
}

.process-step:hover .process-step-icon i {
  color: #ff9e1b;
}

.process-cta {
  margin-top: 80px;
  text-align: center;
}

.btn-process {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 38px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.15rem;
  transition: all 0.4s ease;
  background: linear-gradient(135deg, #2c86e0 0%, #64b5ff 100%);
  color: white;
  text-decoration: none;
  box-shadow: 0 10px 25px rgba(44, 134, 224, 0.25);
}

.btn-process i {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.btn-process:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(255, 158, 27, 0.3);
  background: linear-gradient(135deg, #ff9e1b 0%, #ffb74d 100%);
}

.btn-process:hover i {
  transform: translateX(5px);
}

@media screen and (max-width: 1200px) {
  .process-steps {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media screen and (max-width: 992px) {
  .process-steps {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 768px) {    
  .process-steps {
    grid-template-columns: 1fr;
    margin-top: 60px;
  }
  
  .process-step {
    max-width: 450px;
    margin-left: auto;
    margin-right: auto;
}

.process-step-title {
    font-size: 1.5rem;
}

.process-step-description {
    font-size: 1rem;
  }
  
  .process-step-icon {
    width: 80px;
    height: 80px;
  }
  
  .process-step-icon i {
    font-size: 2.4rem;
  }
}

/* ===== FAQ Section Enhancements ===== */
.investment-faq {
  padding: 100px 0;
  background-color: var(--light-gray);
}

.faq-container {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  background: var(--white);
  border-radius: var(--border-radius-md);
  margin-bottom: 16px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--medium-gray);
  overflow: hidden;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.faq-question:hover {
  background-color: var(--light-accent);
}

.faq-question i {
  transition: transform var(--transition-fast);
  color: var(--accent-color);
}

.faq-item.active .faq-question i {
  transform: rotate(180deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.35s ease;
}

.faq-item.active .faq-answer {
  max-height: 500px;
}

.faq-answer-content {
  padding: 0 25px 20px;
  font-size: 16px;
  line-height: 1.7;
  color: var(--dark-gray);
}

/* ===== CTA Section Enhancements ===== */
.cta {
  padding: 100px 0;
  background: linear-gradient(135deg, #2c86e0 0%, #1a3a6c 100%);
  color: var(--white);
  position: relative;
  overflow: hidden;
}

.cta:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
  background-size: 100px 100px;
  opacity: 0.5;
}

.cta-content {
  max-width: 700px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  text-align: center;
}

.cta h2 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cta p {
  font-size: 18px;
  margin-bottom: 30px;
  line-height: 1.6;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.btn {
  padding: 16px 28px;
  font-size: 16px;
  font-weight: 600;
  border-radius: var(--border-radius-md);
  display: inline-flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn i {
  font-size: 18px;
  transition: transform 0.3s ease;
}

.cta .btn:hover i {
  transform: translateX(5px);
}

.trust-indicators {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.trust-item {
  display: flex;
  align-items: center;
  font-size: 15px;
  color: var(--white);
}

.trust-item i {
  color: #a2d6ff;
  margin-right: 8px;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .calculator-container {
    grid-template-columns: 1fr;
  }
  
  .retirement-hero h1 {
    font-size: 40px;
  }
  
  .retirement-hero p {
    font-size: 16px;
  }
  
  .plans-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  .floating-card {
    display: none;
  }
  
  .benefit-card {
    padding: 25px;
  }
  
  /* Improved responsive styling for retirement illustration */
  .retirement-illustration {
    width: 180px;
    height: 180px;
    margin: 0 auto;
  }
  
  .retirement-showcase {
    min-height: 280px;
  }
  
  .main-icon {
    width: 80px;
    height: 80px;
  }
  
  .main-icon i {
    font-size: 32px;
  }
  
  .icon-1, .icon-2, .icon-3 {
    width: 40px;
    height: 40px;
  }
  
  .icon-1 i, .icon-2 i, .icon-3 i {
    font-size: 18px;
  }
  
  .pulse-circle {
    width: 150px;
    height: 150px;
  }
  
  .pulse-circle::after {
    width: 180px;
    height: 180px;
    top: -15px;
    left: -15px;
  }
  
  .hero-wrapper {
    grid-template-columns: 1fr 1fr;
    gap: 10px;
  }

  .hero-stats {
    gap: 10px;
  }
  
  .hero-stat-item {
    padding: 8px 10px;
    min-width: 90px;
  }
  
  .hero-stat-item .stat-value {
    font-size: 20px;
  }
  
  .hero-stat-item .stat-label {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .retirement-hero {
    padding: 80px 0 60px;
  }
  
  .retirement-hero h1 {
    font-size: 32px;
  }
  
  .retirement-hero-cta {
    flex-direction: column;
    max-width: 300px;
    margin-left: 0;
    margin-right: auto;
  }
  
  /* Better positioning for medium screens */
  .retirement-illustration {
    position: relative;
    margin: 0 auto;
    width: 150px;
    height: 150px;
  }
  
  .retirement-showcase {
    min-height: 240px;
  }
  
  .hero-right {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    min-height: 240px;
  }
  
  .hero-wrapper {
    grid-template-columns: 1fr;
    gap: 10px;
    margin-top: 50px;
  }
  
  .section-header h2 {
    font-size: 28px;
  }
  
  .investment-benefits,
  .investment-plans,
  .investment-calculator,
  .investment-process,
  .investment-faq,
  .cta {
    padding: 70px 0;
  }
  
  /* Responsive behavior */
@media (max-width: 991px) {
  .calculator-container {
      flex-direction: column;
  }
}

@media (max-width: 767px) {
  .result-stats {
      grid-template-columns: 1fr;
  }
}

@media (max-width: 575px) {
  .calculator-form, .calculator-result {
      padding: 20px;
  }

  .calculate-btn {
      width: 100%;
  }
}
  .benefit-icon {
    width: 60px;
    height: 60px;
  }
  
  .benefit-icon i {
    font-size: 24px;
  }
  
  .process-step-content {
    padding: 20px;
  }
  
  .process-step-title {
    font-size: 20px;
  }
  
  .cta h2 {
    font-size: 28px;
  }
  
  .cta-buttons {
    flex-direction: column;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .trust-indicators {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .hero-stats {
    justify-content: flex-start;
    margin-bottom: 20px;
  }
}

@media (max-width: 576px) {
  .retirement-hero h1 {
    font-size: 28px;
  }
  
  .hero-badge {
    font-size: 12px;
  }
  
  /* Smaller retirement illustration for mobile */
  .retirement-illustration {
    width: 120px;
    height: 120px;
    margin: 0 auto;
  }
  
  .retirement-showcase {
    min-height: 200px;
  }
  
  .hero-right {
    min-height: 200px;
  }
  
  .main-icon {
    width: 60px;
    height: 60px;
    border-width: 3px;
  }
  
  .main-icon i {
    font-size: 24px;
  }
  
  .icon-1, .icon-2, .icon-3 {
    width: 30px;
    height: 30px;
    border-width: 2px;
  }
  
  .icon-1 i, .icon-2 i, .icon-3 i {
    font-size: 14px;
  }
  
  .pulse-circle {
    width: 100px;
    height: 100px;
    border-width: 1px;
  }
  
  .pulse-circle::after {
    width: 130px;
    height: 130px;
    top: -15px;
    left: -15px;
    border-width: 1px;
  }
  
  .sebi-badge {
    position: relative;
    bottom: auto;
    right: auto;
    margin: 15px auto 0;
    padding: 6px 12px;
    width: fit-content;
    font-size: 12px;
  }
  
  .sebi-badge i {
    font-size: 12px;
  }
  
  .hero-stats {
    justify-content: space-between;
    gap: 8px;
  }
  
  .hero-stat-item {
    padding: 6px 8px;
    min-width: auto;
    flex: 1;
  }
  
  .hero-stat-item .stat-value {
    font-size: 18px;
  }
  
  .hero-stat-item .stat-label {
    font-size: 11px;
  }
  
  .section-header {
    margin-bottom: 40px;
  }
  
  .benefit-card {
    padding: 20px;
  }
  
  .plan-header {
    padding: 25px 20px;
  }
  
  .plan-features {
    padding: 20px;
  }
  
  .plan-cta {
    padding: 0 20px 20px;
  }
  
  .faq-question {
    padding: 15px 20px;
    font-size: 16px;
  }
  
  .faq-answer-content {
    padding: 0 20px 15px;
  }
}

/* Enhanced Get Started button with arrow */
.navbar-actions .btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background-color: var(--secondary-color);
  color: var(--white);
  padding: 12px 22px;
  border-radius: 30px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  box-shadow: 0 6px 15px rgba(255, 158, 27, 0.25);
  border: none;
}

.navbar-actions .btn-primary::after {
  content: '→';
  display: inline-block;
  font-size: 22px;
  font-weight: 600;
  margin-left: 2px;
  transition: transform 0.3s ease;
  position: relative;
  top: 1px;
}

.navbar-actions .btn-primary:hover {
  background-color: #F6B93B;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(255, 158, 27, 0.35);
}

.navbar-actions .btn-primary:hover::after {
  transform: translateX(5px);
}

/* Enhance benefit link arrow transition */
.benefit-link a i {
  margin-left: 8px;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.benefit-link a:hover i {
  transform: translateX(6px);
}

.cta .btn-primary {
  background: var(--white);
  color: #2c86e0;
  box-shadow: var(--shadow-md);
}

.cta .btn-primary:hover {
  background: var(--light-gray);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.cta .btn-secondary {
  background: transparent;
  color: var(--white);
  border: 2px solid var(--white);
}

.cta .btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
} 