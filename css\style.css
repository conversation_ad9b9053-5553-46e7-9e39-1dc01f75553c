/* Modern Enhanced Global Styles */
:root {
    --primary-color: #1e56a0;
    --primary-dark: #164584;
    --primary-light: #3b7dd4;
    --secondary-color: #163172;
    --accent-color: #f6b93b;
    --accent-dark: #e6a012;
    --dark-color: #0c2340;
    --light-color: #f4f7fc;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --gray-color: #6c757d;
    --gray-light-color: #f8f9fa;
    --shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    --shadow-intense: 0 15px 40px rgba(0, 0, 0, 0.15);
    --shadow-sm: 0 3px 12px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
    --gradient-bg: linear-gradient(135deg, rgba(244, 247, 252, 0.95) 0%, rgba(214, 229, 250, 0.95) 100%);
    
    /* Text colors */
    --text-color: #333;
    --text-light: #6c757d;
    --bg-color: #fff;
    --card-bg: #fff;
}

/* Dark mode variables */
.dark-mode {
    --primary-color: #2a6abf;
    --primary-dark: #1e56a0;
    --primary-light: #4b8de4;
    --secondary-color: #2a4284;
    --accent-color: #f6b93b;
    --accent-dark: #e6a012;
    --dark-color: #0c2340;
    --light-color: #f4f7fc;
    --shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    --shadow-intense: 0 15px 40px rgba(0, 0, 0, 0.25);
    --shadow-sm: 0 3px 12px rgba(0, 0, 0, 0.1);
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
    --gradient-bg: linear-gradient(135deg, rgba(14, 22, 36, 0.95) 0%, rgba(16, 33, 58, 0.95) 100%);
    
    /* Text colors */
    --text-color: #e1e1e1;
    --text-light: #a8b0b9;
    --bg-color: #121212;
    --card-bg: #1e1e1e;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
    overflow-x: hidden;
    position: relative;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Modern Background Effect */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(30, 86, 160, 0.03) 0%, rgba(246, 185, 59, 0.03) 100%);
    z-index: -2;
}

body::after {
    content: '';
    position: fixed;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(30, 86, 160, 0.05) 0%, transparent 70%);
    z-index: -1;
    animation: gradientPulse 15s infinite alternate;
}

@keyframes gradientPulse {
    0% { opacity: 0.3; transform: scale(1); }
    100% { opacity: 0.7; transform: scale(1.2); }
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
    position: relative;
}

a:hover {
    color: var(--accent-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

.section-header {
    margin-bottom: 60px;
    position: relative;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.section-subtitle {
    display: block;
    background-color: var(--dark-color);
    color: #fff !important;
    padding: 6px 18px;
    border-radius: 20px;
    font-size: 0.95rem;
    margin: 0 auto 15px auto;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    max-width: fit-content;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

/* .section-subtitle:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    background-color: rgba(59, 130, 246, 0.15);
} */

.section-header h2 {
    font-size: 2.5rem;
    color: var(--dark-color);
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
    font-weight: 700;
    transition: all 0.3s ease;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    letter-spacing: -0.5px;
}

.client-txt{
    color: var(--accent-color);
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.section-header:hover h2::after {
    width: 100px;
}

.section-header p {
    color: var(--text-light);
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
    text-align: center;
}

section {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.highlight {
    color: var(--accent-color);
    position: relative;
}

/* Modern animated shapes */
.shape-animation {
    position: absolute;
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    z-index: 0;
    display: none; /* Hide all shape animations */
}

.shape-1 {
    top: 10%;
    left: 5%;
    width: 80px;
    height: 80px;
    background-color: rgba(30, 86, 160, 0.1);
    animation: none; /* Disable animation */
}

.shape-2 {
    bottom: 10%;
    right: 5%;
    width: 120px;
    height: 120px;
    background-color: rgba(246, 185, 59, 0.1);
    animation: none; /* Disable animation */
}

.shape-3 {
    top: 50%;
    right: 10%;
    width: 60px;
    height: 60px;
    background-color: rgba(30, 86, 160, 0.08);
    animation: none; /* Disable animation */
}

@keyframes morphShape {
    0% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
    25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
    50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; }
    75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; }
    100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
}

/* Scroll animations */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.appear {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in-left.appear {
    opacity: 1;
    transform: translateX(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in-right.appear {
    opacity: 1;
    transform: translateX(0);
}

/* Page transitions */
.page-transition {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    z-index: 9999;
    transform: scaleY(0);
    transform-origin: bottom;
    transition: transform 0.5s cubic-bezier(0.86, 0, 0.07, 1);
}

.page-transition.active {
    transform: scaleY(1);
    transform-origin: top;
}

/* Scroll progress indicator */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--gradient-accent);
    z-index: 9999;
    transition: width 0.1s;
}

/* Updated Header Styles */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
    z-index: 1000;
    padding: 15px 0;
    transition: all 0.3s ease;
    margin-bottom: 0; /* Remove margin bottom */
}

header.sticky {
    padding: 10px 0;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    position: relative;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.65rem;
}

.brand-logo{
    width: 51px;
  height: 48px;
}
.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    transition: all 0.3s ease;
}

.logo h1:hover {
    transform: translateY(-2px);
}

.logo h1 .highlight {
    color: var(--accent-color);
    position: relative;
    display: inline-block;
}

.logo h1 .highlight::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--accent-color);
    border-radius: 2px;
    transform: scaleX(0.7);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.logo:hover h1 .highlight::after {
    transform: scaleX(1);
}

nav {
    transition: all 0.3s ease;
}

nav ul {
    display: flex;
    gap: 25px;
    align-items: center;
}

nav ul li {
    position: relative;
}

nav ul li a {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    color: var(--dark-color);
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    position: relative;
}

nav ul li a .fas.fa-chevron-down {
    font-size: 0.7rem;
    margin-left: 5px;
    transition: transform 0.2s ease;
}

nav ul li:hover > a {
    color: var(--primary-color);
}

nav ul li:hover > a .fas.fa-chevron-down {
    transform: rotate(180deg);
}

nav ul li a.active {
    color: var(--primary-color);
    font-weight: 600;
}

/* Dropdown menu */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 220px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 100;
    padding: 8px 0;
    border-top: 3px solid var(--primary-color);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    padding: 10px 20px;
    display: block;
    color: var(--dark-color);
    font-size: 0.95rem;
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dropdown-menu a:last-child {
    border-bottom: none;
}

.dropdown-menu a:hover {
    background-color: rgba(30, 86, 160, 0.05);
    color: var(--primary-color);
    padding-left: 25px;
}

.dropdown-toggle {
    position: relative;
    padding-right: 20px !important;
}

.dropdown-toggle::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--dark-color);
    border-bottom: 2px solid var(--dark-color);
    transform: translateY(-70%) rotate(45deg);
    transition: all 0.2s ease;
}

.dropdown:hover .dropdown-toggle::after {
    transform: translateY(-30%) rotate(225deg);
}

.hamburger {
    display: none;
    cursor: pointer;
    width: 30px;
    height: 25px;
    position: relative;
    flex-direction: column;
    transition: all 0.3s ease;
}

.hamburger .bar {
   
    width: 100%;
    height: 3px;
    margin: 5px 0;
    background-color: var(--primary-color);
    transition: all 0.3s ease;
    border-radius: 3px;
}

.hamburger:hover .bar {
    background-color: var(--accent-color);
}

/* Animated hamburger bars */
.hamburger .bar:nth-child(1) {
    transform-origin: left center;
}

.hamburger .bar:nth-child(2) {
    transform-origin: center;
}

.hamburger .bar:nth-child(3) {
    transform-origin: left center;
}

.hamburger .bar.active:nth-child(1) {
    transform: rotate(45deg);
    left: 5px;
}

.hamburger .bar.active:nth-child(2) {
    transform: scaleX(0);
    opacity: 0;
}

.hamburger .bar.active:nth-child(3) {
    transform: rotate(-45deg);
    left: 5px;
}

/* Enhanced Responsive Design - Global Media Queries */
/* Large Devices (Desktops) */
@media screen and (max-width: 1200px) {
    .container {
        width: 95%;
        max-width: 1140px;
    }
    
    .section-header h2 {
        font-size: 2.2rem;
    }
}

/* Medium Devices (Tablets) */
@media screen and (max-width: 992px) {
    .container {
        width: 95%;
        max-width: 960px;
    }
    
    section {
        padding: 80px 0;
    }
    
    .desktop-only {
        display: none;
    }
    
    .mobile-only {
        display: none; /* Initially hidden, shown when menu is active */
    }
    
    .hamburger {
        display: block;
        z-index: 1001; /* Ensure hamburger stays above other elements */
    }
    
    nav {
        position: fixed;
        top: 70px;
        left: 0;
        width: 100%;
        background-color: rgba(255, 255, 255, 0.98);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.5s cubic-bezier(0.77, 0, 0.175, 1);
        z-index: 999;
        padding: 0;
        flex: 1;
    }
    
    nav.active {
        max-height: 80vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    }
    
    nav.active .mobile-only {
        display: flex;
        width: 100%;
        justify-content: center;
        margin: 20px 0 10px 0;
    }
    
    nav ul {
        flex-direction: column;
        padding: 20px;
        gap: 5px;
        width: 100%;
    }
    
    nav ul li {
        width: 100%;
    }
    
    nav ul li a {
        padding: 12px 15px;
        width: 100%;
        justify-content: space-between;
        font-size: 1rem; /* Slightly larger for better touch targets */
    }
    
    .dropdown {
        position: static;
        width: 100%;
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        /* visibility: hidden; */
        transform: none;
        width: 100%;
        box-shadow: none;
        border: none;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease, padding 0.3s ease, visibility 0s;
        padding: 0;
        margin-left: 20px;
        background-color: rgba(0, 0, 0, 0.02);
        border-radius: 5px;
        border-top: none;
    }
    
    .dropdown-menu.active {
        visibility: visible;
        max-height: 500px;
        padding: 10px 0;
    }
    
    .dropdown-menu a {
        padding: 10px 15px; /* Consistent padding */
        font-size: 0.95rem;
    }
    
    .dropdown-toggle.active {
        color: var(--accent-color);
        background-color: rgba(30, 86, 160, 0.05);
    }
    
    .dropdown-toggle.active .fas.fa-chevron-down {
        transform: rotate(180deg);
    }
    
    .dropdown-toggle::after {
        display: inline-block;
    content: '';
        width: 8px;
        height: 8px;
        border-right: 2px solid currentColor;
        border-bottom: 2px solid currentColor;
        margin-left: 10px;
        transform: rotate(45deg);
    transition: transform 0.3s ease;
}

    .dropdown-toggle.active::after {
        transform: rotate(-135deg);
    }
    
    .dropdown:hover .dropdown-menu {
        transform: none;
         /* Override hover behavior on mobile */
    }
}

/* Small Devices (Tablets & Large Phones) */
@media screen and (max-width: 768px) {
    .container {
        width: 95%;
        max-width: 720px;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .section-header p {
        font-size: 1rem;
    }
    
    .hero {
        margin-top: 10px;
        padding-top: 80px;
    }
    
    .hero-left h1 {
        margin-top: 10px;
        font-size: 2rem;
    }
    
    .goal-cards {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 35px;
    }
    
    .goal-card {
        flex: 0 0 calc(50% - 10px); /* 2 cards per row with gap accounted for */
        display: flex;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: var(--border-radius);
        padding: 18px 22px;
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        color: var(--dark-color);
        border: 1px solid rgba(0, 0, 0, 0.05);
        box-shadow: var(--shadow-sm);
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(4px);
        -webkit-backdrop-filter: blur(4px);
    }
    
    @media screen and (max-width: 768px) {
        .goal-cards {
            gap: 16px;
        }
        
        .goal-card {
            flex: 0 0 calc(50% - 8px); /* 2 cards per row on medium screens */
        }
    }
    
    @media screen and (max-width: 576px) {
        .goal-cards {
            gap: 12px;
        }
        
        .goal-card {
            flex: 0 0 100%; /* 1 card per row on smallest screens */
            padding: 15px 18px;
            margin-bottom: 0;
        }
    }
    
    .stat-badge {
        top: 15px;
        right: 15px;
        font-size: 0.9rem;
        padding: 5px 12px;
    }
    
    .hero-action-card h3 {
        font-size: 1.3rem;
    }
    
    /* Improve dropdown menu on mobile */
    .dropdown-menu {
        position: static;
        width: 100%;
        box-shadow: none;
        border-radius: 0;
        margin-top: 0;
        padding: 0 0 0 20px;
    }
    
    .dropdown-menu a {
        padding: 10px 15px;
    }
    
    /* Adjust buttons for better mobile experience */
    .btn-primary, .btn-secondary {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    /* Improve form elements on mobile */
    input, select, textarea {
        font-size: 16px; /* Prevents zoom on focus in iOS */
    }
    
    /* Adjust spacing for mobile */
    section {
        padding: 60px 0;
    }
    
    /* Improve navbar on mobile */
    .navbar-actions {
        margin-right: 50px;
    }
    
    /* Adjust grid layouts for mobile */
    .grid-2, .grid-3, .grid-4 {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

/* Extra Small Devices (Mobile Phones) */
@media screen and (max-width: 576px) {
    .container {
        width: 95%;
        max-width: 540px;
        padding: 0 10px;
    }
    
    .section-header h2 {
        font-size: 1.8rem;
        line-height: 1.3;
    }
    
    .section-header p {
        font-size: 0.95rem;
        line-height: 1.5;
    }
    
    .hero-left h1 {
        font-size: 1.8rem;
        line-height: 1.3;
    }
    
    .hero-left > p {
        font-size: 1.1rem;
        margin-bottom: 20px;
    }
    
    /* Adjust buttons for smaller screens */
    .btn-primary, .btn-secondary {
        padding: 10px 18px;
        font-size: 0.9rem;
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
    }
    
    /* Improve form elements on small mobile */
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-control {
        padding: 12px;
        font-size: 14px;
    }
    
    /* Adjust spacing for mobile */
    section {
        padding: 40px 0;
    }
    
    /* Adjust navbar for small mobile */
    .navbar-actions {
        display: none;
    }
    
    /* Make logo smaller on mobile */
    .logo img {
        height: 45px !important;
    }
    
    /* Adjust card layouts */
    .card {
        padding: 15px;
    }
    
    /* Improve readability of text */
    p {
        font-size: 0.95rem;
        line-height: 1.6;
    }
    
    /* Adjust goal cards for better mobile display */
    .goal-cards {
        grid-template-columns: 1fr;
        gap: 15px;
        margin-bottom: 25px;
    }
    
    .goal-card {
        flex: 1 1 calc(33.333% - 16px); /* 3 cards per row on large screens */
        background: #fff;
        padding: 20px;
        text-align: center;
        border-radius: 8px;
        text-decoration: none;
        color: #333;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: transform 0.2s;
      }
    
    /* Improve featured plan on mobile */
    .featured-plan {
        padding: 20px;
        gap: 15px;
        margin-top: 25px;
    }
    
    .featured-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .featured-content h3 {
        font-size: 1.2rem;
        margin-bottom: 8px;
    }
    
    .featured-content p {
        font-size: 0.9rem;
        margin-bottom: 12px;
    }
}


@media (max-width: 768px) {
    .hamburger {
      display: flex;
    }
  
    nav {
      position: absolute;
      top: 70px;
      left: 0;
      right: 0;
      background: #fff;
      display: none;
      flex-direction: column;
      padding: 20px 0;
      box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
    }
  
    nav.active {
      display: flex;
    }
  
    #menu-items {
      flex-direction: column;
      align-items: center;
      gap: 10px;
    }
  
    .desktop-only {
      display: none;
    }
  
    .dropdown-menu {
      position: relative;
      top: 0;
      box-shadow: none;
      background: #f1f1f1;
    }
  }

/* Services Section */
.services {
    position: relative;
    padding: 80px 0;
    background-color: #fff;
    overflow: hidden;
}

.services::before {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(30, 86, 160, 0.05) 0%, rgba(30, 86, 160, 0) 70%);
    top: -150px;
    left: -150px;
    z-index: 0;
}

.services::after {
    content: '';
    position: absolute;
    width: 250px;
    height: 250px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(246, 185, 59, 0.05) 0%, rgba(246, 185, 59, 0) 70%);
    bottom: -100px;
    right: -100px;
    z-index: 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    position: relative;
    z-index: 1;
}

.service-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 40px 30px;
    box-shadow: var(--shadow-sm);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    text-align: center;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.03);
    z-index: 1;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--gradient-primary);
    transform: translateY(-5px);
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow);
}

.service-card:hover::before {
    transform: translateY(0);
}

.service-shape {
    position: absolute;
    width: 150px;
    height: 150px;
    background-color: var(--primary-color);
    opacity: 0.03;
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    bottom: -75px;
    right: -75px;
    transition: var(--transition);
    z-index: -1;
}

.service-card:hover .service-shape {
    transform: scale(1.2) rotate(10deg);
    opacity: 0.05;
}

.service-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    background: linear-gradient(135deg, rgba(30, 86, 160, 0.1) 0%, rgba(30, 86, 160, 0.2) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 2rem;
    color: var(--accent-color);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    z-index: 1;
}

.service-icon::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 20px;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: -1;
}

.service-card:hover .service-icon {
    color: white;
    transform: rotateY(180deg);
}

.service-card:hover .service-icon::after {
    opacity: 1;
    transform: scale(1);
}

.service-content {
    position: relative;
    z-index: 1;
}

.service-card h3 {
    font-size: 1.6rem;
    margin-bottom: 15px;
    color: var(--dark-color);
    font-weight: 600;
    transition: var(--transition);
}

.service-card:hover h3 {
    color: var(--primary-color);
}

.service-card p {
    margin-bottom: 20px;
    color: var(--gray-color);
    font-size: 1rem;
    line-height: 1.6;
}

.service-link {
    color: var(--primary-color);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    transition: var(--transition);
    padding: 5px 0;
    position: relative;
}

.service-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.service-link i {
    margin-left: 8px;
    font-size: 0.85rem;
    transition: var(--transition);
}

.service-link:hover {
    color: var(--primary-dark);
}

.service-link:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

.service-link:hover i {
    transform: translateX(5px);
}

/* About Section */
.about {
    position: relative;
    padding: 50px 0 40px;
    background-color: #f8f9fa;
    overflow: hidden;
}

.about::before {
    content: '';
    position: absolute;
    top: -150px;
    right: -150px;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(30, 86, 160, 0.08) 0%, rgba(30, 86, 160, 0) 70%);
    z-index: 0;
}

.about::after {
    content: '';
    position: absolute;
    bottom: -150px;
    left: -150px;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(246, 185, 59, 0.08) 0%, rgba(246, 185, 59, 0) 70%);
    z-index: 0;
}

.about-content {
    display: flex;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 2;
    margin-top: 20px;
}

.about-left {
    flex: 1;
    padding-right: 20px;
}

.about-right {
    flex: 1;
}

.about-text-wrapper {
    margin-top: 30px;
}

.about-intro {
    font-size: 1.2rem;
    line-height: 1.7;
    color: var(--dark-color);
    margin-bottom: 30px;
    font-weight: 400;
}

.about-cards {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

.about-card {
    display: flex;
    background: #fff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.about-card:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.about-card-icon {
    margin-right: 20px;
    width: 50px;
    height: 50px;
    min-width: 50px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    box-shadow: 0 8px 15px rgba(30, 86, 160, 0.2);
}

.about-card:hover .about-card-icon {
    transform: scale(1.1);
    background: var(--accent-color);
}

.about-card-content h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--dark-color);
    font-weight: 600;
}

.about-card-content p {
    font-size: 0.95rem;
    line-height: 1.6;
    color: var(--gray-color);
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

.feature-item {
    display: flex;
    align-items: center;
}

.feature-icon {
    width: 24px;
    height: 24px;
    min-width: 24px;
    border-radius: 50%;
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 0.7rem;
}

.feature-text p {
    font-weight: 500;
    color: var(--dark-color);
}

.about-cta {
    display: inline-flex;
    padding: 12px 25px;
    margin-top: 10px;
}

.about-image-container {
    position: relative;
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
    padding: 20px;
    overflow: hidden;
}

.about-main-image {
    width: 100%;
    border-radius: 10px;
    margin-bottom: 20px;
}

.about-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 0 15px;
}

.about-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1.2;
}

.stat-number sup {
    font-size: 0.9rem;
    font-weight: 600;
    top: -0.5em;
}

.stat-label {
    font-size: 0.85rem;
    color: var(--gray-color);
    margin-top: 5px;
}

.about-team-preview {
    background: rgba(30, 86, 160, 0.03);
    border-radius: 12px;
    padding: 15px;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.team-preview-images {
    display: flex;
    margin-right: 15px;
}

.team-image {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid white;
    margin-left: -15px;
    object-fit: cover;
}

.team-image:first-child {
    margin-left: 0;
}

.team-more {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
}

.team-preview-text h4 {
    font-size: 1rem;
    margin-bottom: 5px;
    color: var(--dark-color);
}

.team-preview-text p {
    font-size: 0.85rem;
    color: var(--gray-color);
}

.about-badge {
    position: absolute;
    top: 30px;
    right: 30px;
    background: var(--accent-color);
    color: white;
    padding: 7px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    box-shadow: 0 8px 15px rgba(246, 185, 59, 0.3);
}

.about-badge i {
    margin-right: 5px;
}

.about-shape-1 {
    position: absolute;
    top: -20px;
    left: -20px;
    width: 120px;
    height: 120px;
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    background-color: rgba(30, 86, 160, 0.05);
    z-index: -1;
    animation: morphShape 15s linear infinite, float 8s ease-in-out infinite;
}

.about-shape-2 {
    position: absolute;
    bottom: -20px;
    right: -20px;
    width: 80px;
    height: 80px;
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    background-color: rgba(246, 185, 59, 0.05);
    z-index: -1;
    animation: morphShape 20s linear infinite reverse, float 10s ease-in-out infinite reverse;
}

/* Responsive styles for About section */
@media screen and (max-width: 992px) {
    .section-header.centered h2 { 
        font-size: 28px;
    }
    
    .about-content {
        flex-direction: column;
    }
    
    .about-right {
        margin-top: 30px;
        width: 100%;
    }
    
    .team-illustration {
        transform: scale(0.9);
    }
}

@media screen and (max-width: 768px) {
    .about {
        padding: 40px 0 30px;
    }
    
    .about-card {
        flex-direction: column;
    }
    
    .about-card-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .about-stats {
        flex-wrap: wrap;
        gap: 20px;
    }
    
    .about-stat {
        flex: 0 0 calc(50% - 10px);
    }
    
    .team-illustration {
        height: 250px;
    }
    
    .team-avatar.satellite {
        width: 50px;
        height: 50px;
    }
    
    .team-avatar.central {
        width: 70px;
        height: 70px;
    }
    
    .finance-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

@media screen and (max-width: 576px) {
    .team-illustration {
        height: 220px;
    }
    
    .team-avatar.satellite {
        width: 40px;
        height: 40px;
    }
    
    .team-avatar.central {
        width: 60px;
        height: 60px;
    }
    
    .finance-icon {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }
    
    .team-satellites::before {
        width: 150px;
        height: 150px;
    }
}

/* Process Section */
.process {
    position: relative;
    background: linear-gradient(to right, #f8f9fa 0%, #eef1f5 100%);
    overflow: hidden;
    padding: 120px 0;
}

.dark-mode .process {
    background: linear-gradient(to right, #131722 0%, #1c2030 100%);
}

.process::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231e56a0' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    z-index: 0;
    opacity: 0.5;
}

.process-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-top: 60px;
    position: relative;
    z-index: 2;
}

.process-step-card {
    /* background: var(--card-bg); */
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 30px;
    position: relative;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: 1px solid rgba(30, 86, 160, 0.08);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.6s forwards;
    animation-delay: calc(var(--step) * 0.2s);
}

.process-step-card[data-step="1"] {
    --step: 1;
}

.process-step-card[data-step="2"] {
    --step: 2;
}

.process-step-card[data-step="3"] {
    --step: 3;
}

.process-step-card[data-step="4"] {
    --step: 4;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.process-step-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-intense);
    
}

.process-step-number {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    opacity: 0.15;
    line-height: 1;
    transition: all 0.3s ease;
}

.dark-mode .process-step-number {
    color: var(--primary-light);
    opacity: 0.2;
}

.process-step-card:hover .process-step-number {
    transform: scale(1.2);
    opacity: 0.25;
}

.process-step-icon {
    width: 70px;
    height: 70px;
    /* background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%); */
    background: var(--accent-color);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 10px 20px rgba(30, 86, 160, 0.2);
}

.process-step-card:hover .process-step-icon {
    transform: rotate(-10deg) scale(1.1);
    box-shadow: 0 15px 30px rgba(30, 86, 160, 0.3);
}

.process-step-card h3 {
    font-size: 1.4rem;
    color: var(--dark-color);
    margin-bottom: 15px;
    position: relative;
    font-weight: 600;
}

.dark-mode .process-step-card h3 {
    color: #fff;
}

.process-step-card p {
    color: var(--text-light);
    margin-bottom: 20px;
    line-height: 1.6;
    flex-grow: 1;
}

.process-features {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
}

.process-features li {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--text-color);
    transition: all 0.3s ease;
}

.process-features li:last-child {
    margin-bottom: 0;
}

.process-features li i {
    color: var(--success-color);
    margin-right: 10px;
    font-size: 0.9rem;
}

.process-arrow {
    position: absolute;
    bottom: 20px;
    right: 20px;
    color: var(--primary-color);
    font-size: 1.5rem;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.dark-mode .process-arrow {
    color: var(--primary-light);
}

.process-step-card:hover .process-arrow {
    transform: translateX(10px);
    opacity: 1;
}

.process-cta {
    text-align: center;
    margin-top: 60px;
    padding: 40px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
    z-index: 2;
    animation: fadeIn 1s forwards;
    animation-delay: 1s;
    opacity: 0;
}

.dark-mode .process-cta {
    background: rgba(28, 36, 60, 0.7);
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.process-cta p {
    font-size: 1.3rem;
    margin-bottom: 25px;
    color: var(--dark-color);
    font-weight: 600;
}

.dark-mode .process-cta p {
    color: #fff;
}

.process-cta .btn-primary {
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 50px;
    box-shadow: 0 10px 20px rgba(30, 86, 160, 0.3);
    transition: all 0.3s ease;
}

.process-cta .btn-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(30, 86, 160, 0.4);
}

@media screen and (max-width: 992px) {
    .process-container {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .process-step-card {
        padding: 25px;
    }
    
    .process-step-icon {
        width: 60px;
        height: 60px;
        font-size: 1.3rem;
    }
    
    .process-step-number {
        font-size: 2rem;
    }
}

@media screen and (max-width: 768px) {
    .process {
        padding: 80px 0;
    }
    
    .process-container {
        grid-template-columns: 1fr;
        max-width: 450px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .process-cta {
        padding: 30px 20px;
    }
}

/* Testimonials Section */
.testimonials {
    position: relative;
    padding: 80px 0;
    background-color: #f8f9fa;
    overflow: hidden;
}

.testimonials::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(135deg, rgba(236, 240, 255, 0.5) 0%, rgba(236, 240, 255, 0) 70%);
    z-index: 0;
}

.feedback-container {
    position: relative;
    overflow: hidden;
    padding: 20px 0;
    max-width: 100%;
}

.feedback-container::before,
.feedback-container::after {
    content: '';
    position: absolute;
    top: 0;
    height: 100%;
    width: 80px;
    z-index: 2;
    pointer-events: none;
}

.feedback-container::before {
    left: 0;
    background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
}

.feedback-container::after {
    right: 0;
    background: linear-gradient(to left, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
}

.feedback-grid {
    display: flex;
    gap: 20px;
    padding: 15px 0;
    animation: scroll 30s linear infinite;
}

.feedback-grid:hover {
    animation-play-state: paused;
}

@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(calc(-300px * 4)); /* Adjust based on card width */
    }
}

@media screen and (max-width: 992px) {
    .feedback-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .feedback-card {
        width: 280px;
        height: 170px;
    }
    
    .feedback-card:hover {
        height: 290px;
    }
}

@media screen and (max-width: 768px) {
    .feedback-grid {
        grid-template-columns: repeat(1, 1fr);
        max-width: 450px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .feedback-card {
        width: 300px;
        height: 165px;
    }
    
    .client-avatar {
    width: 50px;
    height: 50px;
    }
}

@media screen and (max-width: 576px) {
    .feedback-card {
        width: 250px;
        height: 170px;
    }
    
    .feedback-card:hover {
        height: 300px;
    }
    
    .feedback-card::after {
        bottom: 10px;
        font-size: 11px;
        padding: 5px 12px;
        width: 100px;
    }
    
    .client-avatar {
        width: 45px;
        height: 45px;
    }
    
    .client-info h3 {
        font-size: 14px;
    }
    
    .client-info p {
        font-size: 12px;
    }
    
    .star-rating {
        gap: 1px;
    }
    
    .star-rating i {
        font-size: 12px;
    }
    
    .client-stats {
        padding: 4px 8px;
        font-size: 13px;
    }
}

.client-avatar {
    width: 55px;
    height: 55px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    background-color: #f3f3f3;
    display: flex;
    align-items: center;
    justify-content: center;
}

.client-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.feedback-card {
    background-color: #fff;
    border-radius: 15px;
    padding: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    flex: 0 0 auto;
    width: 300px;
    height: 200px; /* Increased height for normal view to show client info and stars */
    display: flex;
    flex-direction: column;
    cursor: pointer;
}

.feedback-card::after {
    content: 'Read Review';
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #4864e0;
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    padding: 6px 15px;
    border-radius: 20px;
    opacity: 0; /* Hide initially */
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 3px 10px rgba(72, 100, 224, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: auto;
    text-align: center;
}

.feedback-card:hover,
.feedback-card.expanded {
    transform: translateY(-5px) scale(1.03);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    z-index: 10;
    height: 320px; /* Expand height on hover to show review content */
}

.feedback-card:hover::after,
.feedback-card.expanded::after {
    opacity: 0; /* Keep hidden on hover as we'll use a button instead */
}

.feedback-header {
    display: flex;
    align-items: flex-start;
    padding: 5px 0 15px;
    border-bottom: 1px solid rgba(243, 243, 243, 0.5);
    transition: all 0.4s ease;
}

.feedback-card:hover .feedback-header,
.feedback-card.expanded .feedback-header {
    border-bottom-color: #f3f3f3;
}

.client-info {
    margin-left: 15px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.star-rating {
    margin: 2px 0;
    color: #FFD700;
    display: flex;
    gap: 2px;
}

/* Make the client stats visible in normal view */
.client-stats {
    display: inline-flex;
    align-items: center;
    background-color: rgba(72, 100, 224, 0.08);
    padding: 5px 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
    color: #4864e0;
    margin-top: 2px;
}

.client-stats:hover {
    background-color: rgba(72, 100, 224, 0.15);
}

@media screen and (max-width: 576px) {
    .client-stats {
        padding: 3px 8px;
        font-size: 13px;
        margin-top: 1px;
    }
    
    .stat-number {
        font-size: 14px;
    }
    
    .stat-label {
        font-size: 10px;
    }
}

.stat-number {
    font-size: 16px;
    font-weight: 700;
    color: #4864e0;
    line-height: 1;
    margin-right: 5px;
}

.stat-number sup {
    font-size: 10px;
    font-weight: 600;
    margin-left: 1px;
}

.stat-label {
    font-size: 11px;
    color: #546175;
    line-height: 1;
}

.feedback-content {
    padding: 0;
    max-height: 0;
    opacity: 0;
    overflow: hidden;
    transition: all 0.4s ease;
    visibility: hidden;
    transform: translateY(20px);
    margin-top: auto;
}

.feedback-card:hover .feedback-content {
    padding: 15px 5px 40px; /* Added bottom padding for button */
    max-height: 150px;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.feedback-content p {
    font-size: 14px;
    line-height: 1.5;
    color: #546175;
    position: relative;
    padding-left: 22px;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 4; /* Reduced from 5 to prevent scrolling */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.feedback-content p::before {
    content: '"';
    position: absolute;
    left: 0;
    top: 0;
    font-size: 36px;
    line-height: 0.5;
    color: rgba(72, 100, 224, 0.2);
    font-family: Georgia, serif;
}

/* Add Review Button */
.review-button {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #4864e0;
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    padding: 6px 15px;
    border-radius: 20px;
        opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(72, 100, 224, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    text-align: center;
}

.feedback-card:hover .review-button {
        opacity: 1;
}

/* CTA Section */
.cta {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%), url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDQwIDQwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYxMCIgZmlsbC1vcGFjaXR5PSIwLjA1Ij48cGF0aCBkPSJNMCAwaDQwdjQwSDB6TTE4IDIwaDJ2MmgtMnptMCAwaC0ydi0yaDF2MWgxem0yIDBoMnYtMmgtMXYxaC0xem0yLTJoMnYtMmgtMnpNMjAgMTh2LTJoLTJ2MnptLTItMmgtMnYyaDJ6TTE2IDE2di0yaDJ2LTJoLTJ2MmgtMnYyaDJ6TTE0IDI0djJoMnYtMmgtMnptMi0ydjJoMnYtMmgtMnptMiAwaDJ2LTJoLTJ2MnptMi0yaDJ2LTJoLTJ2MnptMiAwdjJoMnYtMmgtMnptMiAydjJoMnYtMmgtMnptMiAwaDJ2LTJoLTJ2MnptMi00aDJ2LTJoLTJ2MnptLTIgMGgydi0yaC0ydjJ6Ii8+PC9nPjwvZz48L3N2Zz4=');
    text-align: center;
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(246, 185, 59, 0.3), transparent 60%);
    z-index: 0;
}

.cta::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 15px;
    background: linear-gradient(90deg, var(--accent-color), transparent 80%);
    z-index: 1;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
    padding: 20px;
    border-radius: var(--border-radius);
    background-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: fadeIn 1s ease-out, floatCta 5s ease-in-out infinite;
}

@keyframes floatCta {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.cta-content .section-subtitle {
    color: white;
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 20px;
    font-weight: 500;
    transform: none;
    transition: transform 0.3s ease;
}

.cta-content .section-subtitle:hover {
    transform: scale(1.05);
    background-color: rgba(255, 255, 255, 0.2);
}

.cta-content h2 {
    font-size: 2.8rem;
    color: #fff;
    margin-bottom: 20px;
    font-weight: 700;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    position: relative;
    display: inline-block;
}

.cta-content h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--accent-color);
    border-radius: 3px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.cta-content p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 35px;
    font-size: 1.15rem;
    line-height: 1.6;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 25px;
    margin-bottom: 35px;
}

.cta .btn {
    padding: 15px 32px;
    font-size: 1.05rem;
    border-radius: 50px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.cta .btn i {
    margin-right: 10px;
    transition: transform 0.3s ease;
}

.cta .btn:hover i {
    transform: translateY(-3px);
}

.cta .btn-primary {
    background-color: var(--accent-color);
    box-shadow: 0 8px 20px rgba(246, 185, 59, 0.4);
    border: none;
    position: relative;
    overflow: hidden;
}

.cta .btn-primary::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: -100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.cta .btn-primary:hover {
    background-color: var(--accent-dark);
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(246, 185, 59, 0.5);
}

.cta .btn-primary:hover::after {
    left: 100%;
}

.cta .btn-secondary {
    background-color: transparent;
    border: 2px solid white;
    color: white;
    overflow: hidden;
    position: relative;
    z-index: 1;
}

.cta .btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: white;
    transition: all 0.3s ease;
    z-index: -1;
}

.cta .btn-secondary:hover {
    color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.cta .btn-secondary:hover::before {
    width: 100%;
}

.trust-indicators {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 25px;
}

.trust-item {
    display: flex;
    align-items: center;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
    padding: 10px 15px;
    border-radius: 50px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.trust-item:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.trust-item i {
    color: var(--accent-color);
    margin-right: 10px;
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.trust-item:hover i {
    transform: scale(1.2);
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background-color: #f9fafc;
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(72, 100, 224, 0.1) 0%, rgba(72, 100, 224, 0.02) 100%);
    top: -100px;
    left: -100px;
    z-index: 1;
}

.contact::after {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(72, 100, 224, 0.08) 0%, rgba(72, 100, 224, 0.01) 100%);
    bottom: -100px;
    right: -100px;
    z-index: 1;
}

.contact-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin-top: 50px;
    position: relative;
    z-index: 2;
    gap: 30px;
}

.contact-content {
    flex: 1;
    min-width: 300px;
}

.contact-form-container {
    flex: 1.5;
    min-width: 300px;
}

.contact-info-card {
    background-color: #4864e0;
    border-radius: 16px;
    padding: 40px 30px;
    color: #fff;
    height: 100%;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(72, 100, 224, 0.2);
}

.contact-info-header {
    margin-bottom: 30px;
}

.contact-info-header h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 10px;
}

.contact-info-header p {
    font-size: 14px;
    opacity: 0.85;
    line-height: 1.6;
}

.contact-info-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.info-icon {
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.info-content h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.info-content p {
    font-size: 14px;
    opacity: 0.85;
    line-height: 1.5;
}

.contact-social {
    margin-top: 40px;
}

.contact-social h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
}

.social-links {
    display: flex;
    gap: 12px;
}

.social-links a {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 14px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background-color: #fff;
    color: #4864e0;
    transform: translateY(-3px);
}

.contact-decoration {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    transform: translate(50%, 50%);
}

.contact-form {
    background-color: #fff;
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
}

.form-group {
    position: relative;
    margin-bottom: 25px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 15px;
    color: #333;
    background-color: #fff;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #4864e0;
    box-shadow: 0 0 0 3px rgba(72, 100, 224, 0.15);
    outline: none;
}

.form-group .floating-label {
    position: absolute;
    top: 12px;
    left: 16px;
    font-size: 15px;
    color: #888;
    pointer-events: none;
    transition: all 0.3s ease;
}

.form-group input:focus ~ .floating-label,
.form-group select:focus ~ .floating-label,
.form-group textarea:focus ~ .floating-label,
.form-group input:not(:placeholder-shown) ~ .floating-label,
.form-group select:not([value=""]):not([value="null"]) ~ .floating-label,
.form-group textarea:not(:placeholder-shown) ~ .floating-label {
    transform: translateY(-24px) scale(0.85);
    color: #4864e0;
    background-color: #fff;
    padding: 0 5px;
    left: 11px;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: transparent;
}

.form-privacy {
    margin-bottom: 30px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    user-select: none;
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.checkbox-container:hover input ~ .checkmark {
    border-color: #4864e0;
}

.checkbox-container input:checked ~ .checkmark {
    background-color: #4864e0;
    border-color: #4864e0;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-container .checkmark:after {
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-container a {
    color: #4864e0;
    text-decoration: underline;
}

.btn-submit {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 14px 20px;
    background-color: #4864e0;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 10px;
}

.btn-submit i {
    font-size: 14px;
    transition: transform 0.3s ease;
}

.btn-submit:hover {
    background-color: #3a53c5;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(72, 100, 224, 0.15);
}

.btn-submit:hover i {
    transform: translateX(5px);
}

@media screen and (max-width: 992px) {
    .contact-wrapper {
        flex-direction: column;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
}

@media screen and (max-width: 576px) {
    .contact-form {
        padding: 30px 20px;
    }
    
    .contact-info-card {
        padding: 30px 20px;
    }
}

/* Footer Section */
footer {
    background-color: var(--dark-color);
    color: #fff;
    padding: 70px 0 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
}

.footer-logo {
    flex: 2;
    margin-right: 40px;
}

.footer-logo h2 {
    font-size: 2rem;
    margin-bottom: 15px;
}

.footer-logo p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 20px;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: #fff;
    transition: var(--transition);
}

.social-icons a:hover {
    background-color: var(--accent-color);
}

.footer-links {
    flex: 3;
    display: flex;
    justify-content: space-between;
}

.footer-column {
    flex: 1;
}

.footer-column h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-column h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--accent-color);
}

.footer-column ul li {
    margin-bottom: 10px;
}

.footer-column ul li a {
    color: rgba(255, 255, 255, 0.7);
    transition: var(--transition);
}

.footer-column ul li a:hover {
    color: var(--accent-color);
    padding-left: 5px;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    margin-top: 40px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.7);
}

.footer-legal {
    display: flex;
    gap: 20px;
}

.footer-legal li a {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

/* Responsive Styles */
@media screen and (max-width: 992px) {
    .hero-content h1 {
        font-size: 2.8rem;
    }
    
    .about-content,
    .contact-content {
        flex-direction: column;
    }
    
    .about-image {
        order: -1;
        margin-bottom: 30px;
    }
    
    .process-steps {
        flex-wrap: wrap;
    }
    
    .process-steps::before {
        display: none;
    }
    
    .process-step {
        flex: 0 0 50%;
        margin-bottom: 40px;
    }
    
    .footer-content {
        flex-direction: column;
    }
    
    .footer-logo {
        margin-bottom: 30px;
        margin-right: 0;
    }
    
    .footer-links {
        gap: 30px;
    }
}

@media screen and (max-width: 768px) {
    .navbar {
        padding: 10px 0;
    }
    
    .hamburger {
        display: block;
    }
    
    nav {
        position: fixed;
        top: 70px;
        left: 0;
        width: 100%;
        background-color: rgba(255, 255, 255, 0.98);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.5s cubic-bezier(0.77, 0, 0.175, 1);
        z-index: 999;
    }
    
    nav.active {
        max-height: 80vh;
    }
    
    nav ul {
        flex-direction: column;
        padding: 20px;
        gap: 15px;
    }
    
    nav ul li {
        margin: 0;
        width: 100%;
    }
    
    nav ul li a {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border-radius: 5px;
        transition: all 0.3s ease;
        min-height: 44px; /* Minimum touch target height */
    }
    
    nav ul li a:hover {
        background-color: rgba(30, 86, 160, 0.08);
        padding-left: 18px;
    }
    
    .hero .container {
        flex-direction: column;
        text-align: center;
    }
    
    .hero-content {
        margin-bottom: 40px;
    }
    
    .cta-buttons {
        justify-content: center;
    }
    
    .stats-container {
        flex-direction: column;
        gap: 20px;
    }
    
    .testimonial-content {
        margin: 0;
    }
    
    .process-step {
        flex: 0 0 100%;
    }
    
    .footer-links {
        flex-direction: column;
    }
    
    .footer-column {
        margin-bottom: 30px;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .footer-legal {
        justify-content: center;
    }
}

@media screen and (max-width: 576px) {
    .section-header h2 {
        font-size: 2rem;
    }
    
    .hero-content h1 {
        font-size: 2.2rem;
    }
    
    .hero-content h2 {
        font-size: 1.5rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .btn {
        width: 100%;
    }
} 

/* Navbar actions section styling */
.navbar-actions {
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.btn-primary {
    background-color: var(--accent-color);
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(30, 86, 160, 0.25);
    text-transform: none;
    letter-spacing: 0.5px;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-primary:hover {
    background-color: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(30, 86, 160, 0.35);
    color: white;
}

/* Mobile styles for navbar actions */
@media screen and (max-width: 992px) {
    .navbar-actions {
        display: none;
    }
    
    /* Show the button at the bottom of the mobile menu when active */
    nav.active .navbar-actions {
        display: flex;
        width: 100%;
        justify-content: center;
        margin: 20px 0 10px 0;
    }
    
    nav.active .btn-primary {
        width: 90%;
    }
}

.section-header.centered {
    text-align: center;
    margin-bottom: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.section-header.centered h2 {
    font-size: 2.8rem;
    color: var(--dark-color);
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    max-width: 700px;
}

.title-underline {
    width: 80px;
    height: 4px;
    background-color: var(--primary-color);
    margin: 0 auto;
    margin-top: 10px;
    border-radius: 2px;
}

/* Team Illustration Styling */
.team-illustration {
    position: relative;
    width: 100%;
    height: 300px;
    background-color: #f8fafd;
    border-radius: 12px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.team-central {
    position: relative;
    z-index: 3;
}

.team-avatar {
    width: 60px;
    height: 60px;
    background-color: #1e56a0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.team-avatar.central {
    width: 80px;
    height: 80px;
    background-color: #163172;
    font-size: 2rem;
    position: relative;
    z-index: 4;
}

.team-satellites {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.team-avatar.satellite {
    position: absolute;
    width: 60px;
    height: 60px;
}

.team-avatar.top-left {
    top: 20%;
    left: 20%;
}

.team-avatar.top-right {
    top: 20%;
    right: 20%;
}

.team-avatar.bottom-left {
    bottom: 20%;
    left: 20%;
}

.team-avatar.bottom-right {
    bottom: 20%;
    right: 20%;
}

.finance-icons {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.finance-icon {
    position: absolute;
    width: 40px;
    height: 40px;
    background-color: #f6b93b;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    font-size: 1.2rem;
}

.finance-icon.dollar {
    top: 15%;
    left: 10%;
}

.finance-icon.percent {
    top: 15%;
    right: 10%;
}

.finance-icon.pound {
    bottom: 15%;
    left: 10%;
}

.finance-icon.euro {
    bottom: 15%;
    right: 10%;
}

.connector-lines {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 2;
}

.connector-lines::before,
.connector-lines::after {
    content: '';
    position: absolute;
    background-color: rgba(30, 86, 160, 0.1);
    z-index: 1;
}

.connector-lines::before {
    width: 100%;
    height: 2px;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
}

.connector-lines::after {
    width: 2px;
    height: 100%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}

/* Updated Team Preview */
.team-preview-images {
    display: flex;
    margin-right: 15px;
}

.team-image {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #1e56a0;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid white;
    margin-left: -15px;
    z-index: 1;
    position: relative;
}

.team-image:first-child {
    margin-left: 0;
    z-index: 3;
}

.team-image:nth-child(2) {
    z-index: 2;
}

.team-more {
    background: var(--accent-color);
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
}

/* Adjusted About Badge */
.about-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--accent-color);
    color: white;
    padding: 7px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    box-shadow: 0 8px 15px rgba(246, 185, 59, 0.3);
    z-index: 5;
}

.team-illustration::before,
.team-illustration::after {
    content: '';
    position: absolute;
    background-color: rgba(246, 185, 59, 0.1);
    border-radius: 50%;
}

.team-illustration::before {
    width: 150px;
    height: 150px;
    top: -70px;
    right: -70px;
}

.team-illustration::after {
    width: 120px;
    height: 120px;
    bottom: -60px;
    left: -60px;
}

/* Add connection lines between avatars */
.team-satellites::before,
.team-satellites::after {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    border: 2px solid rgba(30, 86, 160, 0.1);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.team-satellites::before {
    width: 180px;
    height: 180px;
}

.team-satellites::after {
    width: 120px;
    height: 120px;
}

.feedback-container {
    position: relative;
    width: 100%;
    max-width: 1100px;
    margin: 0 auto;
    overflow: hidden;
    padding: 10px 0;
}

.feedback-container::before,
.feedback-container::after {
    content: '';
    position: absolute;
    top: 0;
    width: 80px;
    height: 100%;
    z-index: 3;
    pointer-events: none;
}

.feedback-container::before {
    left: 0;
    background: linear-gradient(to right, #f8f9fa 0%, rgba(248, 249, 250, 0) 100%);
}

.feedback-container::after {
    right: 0;
    background: linear-gradient(to left, #f8f9fa 0%, rgba(248, 249, 250, 0) 100%);
}

.testimonial-cta {
    text-align: center;
    margin-top: 40px;
}

.testimonial-cta .btn-primary {
    padding: 14px 30px;
    font-size: 16px;
    box-shadow: 0 5px 15px rgba(72, 100, 224, 0.15);
    transition: all 0.3s ease;
}

.testimonial-cta .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(72, 100, 224, 0.25);
}

/* Dark mode toggle */
.dark-mode-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    cursor: pointer;
    margin-left: 15px;
    color: var(--primary-color);
    font-size: 1.2rem;
    transition: var(--transition);
}

.dark-mode-toggle:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transform: rotate(30deg);
}

.dark-mode .dark-mode-toggle {
    color: var(--accent-color);
}

.dark-mode .dark-mode-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
} 

/* Enhanced navbar Get Started button with arrow */
.navbar-actions .btn-primary {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 10px 20px 10px 24px;
  background-color: #F6B93B;
  color: #fff;
  font-weight: 600;
  border-radius: 30px;
  overflow: hidden;
  box-shadow: 0 6px 15px rgba(255, 158, 27, 0.25);
  transition: all 0.3s ease;
}

.navbar-actions .btn-primary::after {
  content: '→';
  display: inline-block;
  font-size: 24px;
  margin-left: 8px;
  font-weight: 400;
  transition: transform 0.3s ease;
  line-height: 0;
  position: relative;
  top: 2px;
}

.navbar-actions .btn-primary:hover {
  background-color: #F6B93B;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(255, 158, 27, 0.35);
}

.navbar-actions .btn-primary:hover::after {
  transform: translateX(5px);
} 

/* Navbar Logo Styling */
.navbar-logo {
  max-height: 55px;
  width: auto;
  display: block;
  transition: transform 0.3s ease;
}

.logo a {
  display: inline-block;
  text-decoration: none;
}

.logo a:hover .navbar-logo {
  transform: scale(1.05);
}

@media screen and (max-width: 992px) {
  .navbar-logo {
    max-height: 45px;
  }
}

@media screen and (max-width: 768px) {
  .navbar-logo {
    max-height: 40px;
  }
}

@media screen and (max-width: 576px) {
  .navbar-logo {
    max-height: 35px;
  }
}

.footer-logo {
  margin-bottom: 30px;
}

.footer-logo-img {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.footer-logo-image {
  max-width: 160px;
  height: auto;
}

.footer-logo p {
  font-size: 14px;
  color: var(--dark-gray);
  line-height: 1.6;
} 

/* Updated dropdown menu styles for mobile */
/* @media screen and (max-width: 992px) {
    .dropdown:hover .dropdown-menu {
        transform: none;
         /* Override hover behavior on mobile */
     

/* Improve touch targets for smaller screens */
@media screen and (max-width: 768px) {
    body.nav-active {
        overflow: hidden;
        position: fixed;
        width: 100%;
        height: 100%;
    }
    
    .goal-cards {
        gap: 16px;
    }
    
    .goal-card {
        flex: 0 0 calc(50% - 8px); /* 2 cards per row on medium screens */
    }
}

@media screen and (max-width: 576px) {
    .goal-cards {
        gap: 12px;
    }
    
    .goal-card {
        flex: 0 0 100%; /* 1 card per row on smallest screens */
        padding: 15px 18px;
        margin-bottom: 0;
    }
}

/* Updated Hero Section */
.hero {
    position: relative;
    padding-top: 80px; /* Reduced from 100px to 80px */
    padding-bottom: 50px;
    background: linear-gradient(135deg, rgba(240, 246, 255, 0.9), rgba(255, 251, 240, 0.9));
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    z-index: 1;
}

/* Add animated background elements */
.hero::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(30, 86, 160, 0.1) 0%, rgba(30, 86, 160, 0) 70%);
    border-radius: 50%;
    animation: float 20s infinite alternate;
    z-index: 0;
}

.hero::after {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(246, 185, 59, 0.1) 0%, rgba(246, 185, 59, 0) 70%);
    border-radius: 50%;
    animation: float 15s infinite alternate-reverse;
    z-index: 0;
}

.hero-wrapper {
    display: flex;
    flex-direction: row;
    gap: 40px;
    margin-top: 20px;
    position: relative;
    z-index: 1;
}

.hero-left {
    flex: 1;
    max-width: 600px;
    position: relative;
    animation: fadeInUp 1s ease-out;
}

.hero-left h1 {
    font-size: 2.5rem;
    line-height: 1.2;
    color: var(--dark-color);
    margin-bottom: 15px;
    font-weight: 700;
    max-width: 540px;
    background: linear-gradient(120deg, var(--dark-color) 0%, var(--primary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    z-index: 2;
}

.hero-left > p {
    font-size: 1.25rem;
    color: var(--primary-dark);
    margin-bottom: 30px;
    font-weight: 500;
    padding-left: 15px;
    border-left: 4px solid var(--accent-color);
    transform: translateX(0);
    transition: transform 0.3s ease;
}

.hero-left > p:hover {
    transform: translateX(5px);
}

.goal-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 35px;
}

.goal-card {
    flex: 0 0 calc(50% - 10px); /* 2 cards per row with gap accounted for */
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: var(--border-radius);
    padding: 18px 22px;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    color: var(--dark-color);
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

@media screen and (max-width: 768px) {
    .goal-cards {
        gap: 16px;
    }
    
    .goal-card {
        flex: 0 0 calc(50% - 8px); /* 2 cards per row on medium screens */
    }
}

@media screen and (max-width: 576px) {
    .goal-cards {
        gap: 12px;
    }
    
    .goal-card {
        flex: 0 0 100%; /* 1 card per row on smallest screens */
        padding: 15px 18px;
        margin-bottom: 0;
    }
}

.goal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 0;
    background: var(--gradient-accent);
    transition: height 0.3s ease;
    border-radius: 0 2px 2px 0;
}

.goal-card:hover {
    background-color: white;
    transform: translateY(-7px);
    box-shadow: var(--shadow);
    color: var(--dark-color);
    border-color: rgba(246, 185, 59, 0.2);
}

.goal-card:hover::before {
    height: 100%;
}

.goal-icon {
    width: 45px;
    height: 45px;
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    background: linear-gradient(135deg, rgba(246, 185, 59, 0.2) 0%, rgba(246, 185, 59, 0.3) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: var(--accent-color);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-size: 1.1rem;
}

.goal-card:hover .goal-icon {
    background: var(--gradient-accent);
    color: white;
    transform: rotate(-8deg) scale(1.15);
    border-radius: 40% 60% 60% 40% / 40% 40% 60% 60%;
}

.goal-card p {
    flex: 1;
    font-size: 0.95rem;
    font-weight: 500;
    margin: 0;
    transition: transform 0.3s ease;
}

.goal-card:hover p {
    transform: translateX(8px);
    color: var(--primary-color);
}

.goal-card .fa-arrow-right {
    font-size: 0.8rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
    opacity: 0.7;
}

.goal-card:hover .fa-arrow-right {
    transform: translateX(5px);
    opacity: 1;
    color: var(--accent-color);
}

.featured-plan {
    background: rgba(30, 86, 160, 0.05);
    border-radius: var(--border-radius);
    padding: 28px;
    display: flex;
    align-items: flex-start;
    gap: 22px;
    margin-top: 35px;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(30, 86, 160, 0.1);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.featured-plan:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow);
    background: linear-gradient(135deg, rgba(30, 86, 160, 0.08) 0%, rgba(30, 86, 160, 0.03) 100%);
    border-color: rgba(30, 86, 160, 0.2);
}

.featured-plan::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(246, 185, 59, 0.2) 0%, rgba(246, 185, 59, 0) 70%);
    border-radius: 0 0 0 100%;
    z-index: 0;
    transition: all 0.4s ease;
}

.featured-plan:hover::before {
    transform: scale(1.2);
    opacity: 0.8;
}

.featured-icon {
    width: 60px;
    height: 60px;
    border-radius: 18px;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.4rem;
    box-shadow: 0 8px 20px rgba(30, 86, 160, 0.3);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    z-index: 1;
}

.featured-plan:hover .featured-icon {
    transform: rotate(-12deg) scale(1.1);
    border-radius: 20px 12px 20px 12px;
    box-shadow: 0 12px 25px rgba(30, 86, 160, 0.4);
}

.featured-content {
    flex: 1;
    position: relative;
    z-index: 1;
}

.featured-content h3 {
    font-size: 1.35rem;
    color: var(--dark-color);
    margin-bottom: 12px;
    font-weight: 600;
    transition: color 0.3s ease;
}

.featured-plan:hover .featured-content h3 {
    color: var(--primary-color);
}

.featured-content p {
    font-size: 0.95rem;
    color: var(--gray-color);
    margin-bottom: 18px;
    line-height: 1.5;
}

.featured-cta {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    padding: 5px 0;
    position: relative;
}

.featured-cta::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-primary);
    transition: transform 0.3s ease, opacity 0.3s ease;
    transform: scaleX(0.6);
    transform-origin: left;
    opacity: 0.6;
}

.featured-cta:hover::after {
    transform: scaleX(1);
    opacity: 1;
}

.featured-cta::before {
    content: '→';
    opacity: 0;
    margin-left: 0;
    transition: all 0.3s ease;
    transform: translateX(-10px);
}

.featured-cta:hover::before {
    opacity: 1;
    margin-left: 8px;
    transform: translateX(0);
}

.hero-right {
    flex: 0 0 45%;
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    padding: 10px;
}

.hero-stat-card {
    position: relative;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 20px;
    width: 100%;
    max-width: 380px;
    height: auto;
    display: flex;
    flex-direction: column;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: 1px solid rgba(30, 86, 160, 0.08);
    overflow: hidden;
}

.hero-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-intense);
}

.hero-action-card {
    position: relative;
    background: var(--primary-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 20px;
    width: 100%;
    max-width: 380px;
    height: auto;
    display: flex;
    flex-direction: column;
    color: white;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    overflow: hidden;
}

.hero-action-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-intense);
}

.hero-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 0;
}

.hero-action-card h3 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
    color: white;
    font-weight: 600;
}

.score-preview {
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
}

.score-meter {
    height: 12px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    position: relative;
    margin-bottom: 8px;
    overflow: hidden;
}

.meter-fill {
    position: absolute;
    height: 100%;
    width: 0;
    background: var(--accent-color);
    border-radius: 10px;
    transition: width 1.5s cubic-bezier(0.19, 1, 0.22, 1);
}

.hero-action-card:hover .meter-fill {
    width: 75%;
}

.score-value {
    position: absolute;
    top: 50%;
    left: 75%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: 700;
    font-size: 0.85rem;
    opacity: 0;
    transition: opacity 0.6s ease 0.8s;
}

.hero-action-card:hover .score-value {
    opacity: 1;
}

.score-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
}

.btn-hero-cta {
    padding: 12px 18px;
    border-radius: 6px;
    background-color: var(--accent-color);
    color: white;
    font-weight: 500;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    margin-bottom: 15px;
}

.btn-hero-cta:hover {
    background-color: var(--accent-dark);
    transform: translateY(-2px);
}

.btn-hero-cta i {
    transition: transform 0.3s ease;
}

.btn-hero-cta:hover i {
    transform: translateY(-2px);
}

.hero-action-badge {
    text-align: center;
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    position: relative;
    z-index: 1;
}

.hero-action-badge i {
    color: var(--accent-color);
}

.stat-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--accent-color);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85rem;
    box-shadow: 0 3px 10px rgba(246, 185, 59, 0.3);
    z-index: 2;
}

.stat-badge .pulse-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background: var(--accent-color);
    border-radius: 20px;
    opacity: 0.5;
    animation: pulse 2s ease-out infinite;
    z-index: -1;
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.5;
    }
    70% {
        opacity: 0;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

.hero-chart {
    width: 100%;
    margin: 20px 0;
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.stat-highlights {
    display: flex;
    justify-content: space-between;
    margin-top: auto;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.3s ease;
}

.hero-stat-card:hover .stat-item {
    transform: translateY(-3px);
}

@media screen and (max-width: 1100px) {
    .hero-wrapper {
        flex-direction: column;
    }
    
    .hero-left, .hero-right {
        max-width: 100%;
    }
}

/* Rest of styles */
.section-header.centered {
    text-align: center;
    margin-bottom: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
} 

/* Update hero sections to adjust for header without margin */
.hero-section, 
.about-section,
.investment-hero,
.retirement-hero,
.status-hero,
.faq-hero,

.claim-hero {
    margin-top: 5px; /* Remove margin-top */
}
 
/* Specifically for claim-hero that has a defined margin-top in its inline styles */


/* Mobile Navigation Styles */
@media screen and (max-width: 992px) {
    header {
        position: fixed;
        width: 100%;
        background-color: #fff;
        z-index: 1000;
    }
    
    .navbar {
        padding: 15px 0;
    }
    
    .hamburger {
        display: block;
        width: 24px;
        height: 18px;
        position: relative;
        margin-left: auto;
        z-index: 1001;
        cursor: pointer;
    }
    
    .hamburger .bar {
        display: block;
        width: 100%;
        height: 2px;
        margin: 4px 0;
        background-color: var(--primary-color);
        transition: all 0.3s ease;
    }
    
    nav {
        position: fixed;
        top: 0;
        left: -100%;
        width: 85%;
        height: 100vh;
        background-color: #fff;
        padding: 80px 20px 30px;
        overflow-y: auto;
        transition: all 0.4s ease;
        z-index: 1000;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    }
    
    nav.active {
        left: 0;
    }
    
    nav ul {
        flex-direction: column;
        width: 100%;
    }
    
    nav ul li {
        width: 100%;
        margin: 0;
        border-bottom: 1px solid #f1f1f1;
    }
    
    nav ul li a {
        padding: 12px 0;
        font-size: 16px;
        display: block;
        width: 100%;
        color: var(--dark-color);
    }
    
    .dropdown-toggle {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .dropdown-toggle::after {
        
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        font-size: 16px;
        transition: all 0.3s ease;
    }
    
    .dropdown-toggle.active::after {
        transform: rotate(180deg);
    }
    
    .dropdown-menu {
        position: static;
        width: 100%;
        opacity: 1;
        visibility: hidden;
        max-height: 0;
        overflow: hidden;
        box-shadow: none;
        background-color: #f9f9f9;
        border-radius: 0;
        transition: max-height 0.3s ease, visibility 0s;
        margin-left: 15px;
        transform: none;
        padding: 0;
    }
    
    .dropdown-menu.active {
        visibility: visible;
        max-height: 500px;
        padding: 5px 0;
    }
    
    .dropdown-menu a {
        padding: 10px 15px;
        color: var(--dark-color);
        border-bottom: 1px solid #f1f1f1;
    }
    
    .dropdown-menu a:last-child {
        border-bottom: none;
    }
    
    /* Plan your investments button */
    nav .navbar-actions {
        display: block;
        width: 100%;
        margin-top: 20px;
    }
    
    nav .navbar-actions .btn-primary {
        width: 100%;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #FFECB8;
        color: var(--dark-color);
        border: none;
        border-radius: 8px;
        padding: 12px 15px;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    nav .navbar-actions .btn-primary i {
        color: #FDB913;
        font-size: 20px;
    }

    /* Prevent body scrolling when menu is open */
    body.nav-active {
        overflow: hidden;
    }
    
    /* Overlay */
    body.nav-active:before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
    }
    
    /* Hamburger animation */
    .hamburger .bar.active:nth-child(1) {
        transform: translateY(-9px) rotate(45deg);
    }
    
    .hamburger .bar.active:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger .bar.active:nth-child(3) {
        transform: translateY(-6px) rotate(-45deg);
    }
    
    /* Back button */
    .back-btn {
        position: absolute;
        top: 20px;
        left: 20px;
        display: flex;
        align-items: center;
        color: var(--primary-color);
        font-size: 16px;
        font-weight: 500;
    }
    
    .back-btn i {
        margin-right: 8px;
        font-size: 18px;
    }
}

/* Enhanced Mobile Navigation Styles */
@media screen and (max-width: 992px) {
    /* Hamburger menu */
    .hamburger {
        display: block !important;
        cursor: pointer;
        z-index: 1001;
    }
    
    .hamburger .bar {
        width: 25px;
        height: 3px;
        background-color: var(--dark-color);
        margin: 5px 0;
        transition: all 0.4s ease;
        display: block;
    }
    
    .hamburger .bar.active:nth-child(1) {
        transform: rotate(-45deg) translate(-7px, 4px);
    }
    
    .hamburger .bar.active:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger .bar.active:nth-child(3) {
        transform: rotate(45deg) translate(-18px, -18px);
    }
    
    /* Navigation menu */
    #nav-menu {
        position: fixed;
        top: 0;
        right: -100%;
        width: 80%;
        max-width: 300px;
        height: 100vh;
        background-color: #fff;
        z-index: 1000;
        transition: right 0.4s ease;
        overflow-y: auto;
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
        padding-top: 70px;
    }
    
    #nav-menu.active {
        right: 0;
    }
    
    body.nav-active {
        overflow: hidden;
    }
    
    /* Menu items */
    #menu-items {
        display: block;
        padding: 0;
        margin: 0;
    }
    
    #menu-items li {
        display: block;
        margin: 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    #menu-items li a {
        padding: 15px 20px;
        display: block;
        color: var(--dark-color);
    }
    
    /* Dropdown handling */
    .dropdown {
        position: relative;
    }

    .Growwell-txt{
        font-weight: bold;
        display: flex;
        align-items: center;
    }
    
    .dropdown-toggle {
        display: flex !important;
        justify-content: space-between;
        align-items: center;
    }
    
    .dropdown-toggle::after {
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        transition: transform 0.3s ease;
    }
    
    .dropdown.active .dropdown-toggle::after {
        transform: rotate(180deg);
    }
    
    .dropdown-menu {
        position: static;
        width: 100%;
        max-height: 0;
        overflow: hidden;
        transition: all 0.4s ease;
        box-shadow: none;
        border-radius: 0;
        background-color: #f8f9fa;
        padding: 0;
        margin: 0;
        top: auto;
        left: auto;
        transform: none;
    }
    
    .dropdown.active .dropdown-menu {
        opacity: 1;
        visibility: visible;
        max-height: 500px;
        padding: 10px 0;
    }
    
    .dropdown-menu a {
        padding: 10px 30px !important;
        color: var(--dark-color);
        font-size: 0.9rem;
    }
    
    .dropdown-menu a:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }
}