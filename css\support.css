 /* Support Page Specific Styles */
 /* Support Hero Section */
 :root {
    --primary-color: #1e56a0;
    --primary-dark: #164584;
    --primary-light: #3b7dd4;
    --secondary-color: #163172;
    --accent-color: #f6b93b;
    --accent-dark: #e6a012;
    --dark-color: #0c2340;
    --light-color: #f4f7fc;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --gray-color: #6c757d;
    --gray-light-color: #f8f9fa;
    --shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    --shadow-intense: 0 15px 40px rgba(0, 0, 0, 0.15);
    --shadow-sm: 0 3px 12px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
    --gradient-bg: linear-gradient(135deg, rgba(244, 247, 252, 0.95) 0%, rgba(214, 229, 250, 0.95) 100%);
    
    /* Text colors */
    --text-color: #333;
    --text-light: #6c757d;
    --bg-color: #fff;
    --card-bg: #fff;
}

 .support-hero {
    background: linear-gradient(135deg, var(--accent-color), var(--success-color));
        color: #ffffff;
        padding: 100px 20px;
        text-align: center;
        /* margin-top: 100px; */
        margin-top: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
}

.support-hero::before {
  content: "";
  position: absolute;
  width: 400px;
  height: 400px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  top: -100px;
  left: -100px;
  z-index: 0;
}


.support-hero::after {
  content: "";
  position: absolute;
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  bottom: -80px;
  right: -80px;
  z-index: 0;
}

.support-txt {
    color: var(--accent-color);
}

.support-hero .container {
  position: relative;
  z-index: 1;
  max-width: 900px;
  margin: 0 auto;
}

.support-hero-content h1 {
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
  line-height: 1.2;
}

.support-hero-content p {
  font-size: 1.2rem;
  color: #cfd8dc;
}

        .support-section {
            background-color: var(--light-color);
            position: relative;
            overflow: hidden;
        }
        
        .support-container {
            position: relative;
            z-index: 2;
        }
        
        .support-options {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }
        
        .support-option {
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            padding: 30px;
            transition: var(--transition);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .support-option:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow);
        }
        
        .support-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--accent-color);
        }
        
        .support-icon {
            width: 80px;
            height: 80px;
            background: rgba(30, 86, 160, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: var(--accent-color);
            font-size: 32px;
            transition: var(--transition);
        }
        
        .support-option:hover .support-icon {
            background: var(--accent-color);
            color: #fff;
            transform: scale(1.1);
        }
        
       

        .support-option h3 {
            margin-bottom: 15px;
            color: var(--dark-color);
            font-size: 22px;
        }
        
        .support-option p {
            color: var(--gray-color);
            margin-bottom: 20px;
        }
        
        .support-option .btn-primary {
            display: inline-block;
            padding: 10px 25px;
            background: var(--accent-color);
            color: #fff;
            border-radius: 30px;
            font-weight: 500;
            transition: var(--transition);
        }
        
        .support-option .btn-primary:hover {
            background: var(--accent-dark);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(30, 86, 160, 0.3);
        }
        
        .support-ticket {
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 40px;
            margin-bottom: 60px;
            position: relative;
            overflow: hidden;
        }
        
        .support-ticket::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--accent-color);
        }
        
        .support-ticket h2 {
            margin-bottom: 30px;
            color: var(--dark-color);
            position: relative;
            display: inline-block;
        }
        
        .support-ticket h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--primary-color);
            border-radius: 2px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark-color);
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e1e1e1;
            border-radius: var(--border-radius-sm);
            font-family: 'Poppins', sans-serif;
            font-size: 16px;
            transition: var(--transition);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(30, 86, 160, 0.1);
        }
        
        textarea.form-control {
            min-height: 150px;
            resize: vertical;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        /* Tablet Devices */
        @media (max-width: 992px) {
            .support-hero-content h1 {
                font-size: 2.8rem;
            }
            .support-container {
                max-width: 95%;
            }
            .support-option {
                padding: 25px 20px;
            }
            .support-channels {
                gap: 20px;
            }
        }
        
        /* Mobile Devices */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            .support-options {
                grid-template-columns: 1fr;
            }
            .support-hero-content h1 {
                font-size: 2.5rem;
            }
            .faq-item {
                padding: 20px;
            }
            .support-hero {
                padding: 80px 20px;
            }
            .support-channels {
                flex-direction: column;
            }
            .support-channel {
                width: 100%;
            }
            .contact-methods {
                flex-direction: column;
            }
            .contact-method {
                width: 100%;
                margin-bottom: 15px;
            }
        }
        
        /* Small Mobile Devices */
        @media (max-width: 576px) {
            .support-hero-content h1 {
                font-size: 2rem;
            }
            .support-hero-content p {
                font-size: 1rem;
            }
            .support-hero {
                padding: 60px 15px;
            }
            .section-header h2 {
                font-size: 1.8rem;
            }
            .support-option h3 {
                font-size: 1.3rem;
            }
            .support-option {
                padding: 20px 15px;
            }
            .faq-item h3 {
                font-size: 1.1rem;
            }
            .faq-item p {
                font-size: 0.9rem;
            }
            .support-option-icon {
                width: 50px;
                height: 50px;
                font-size: 1.5rem;
            }
        }
        
        .form-success {
            display: none;
            background-color: rgba(40, 167, 69, 0.1);
            border-left: 4px solid #28a745;
            padding: 15px;
            border-radius: var(--border-radius-sm);
            margin-bottom: 20px;
            animation: fadeIn 0.5s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .faq-preview {
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 40px;
            margin-bottom: 60px;
            position: relative;
        }

        .guide-txt{
            color: var(--accent-color);
        }
        
        .faq-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--accent-color);
        }
        
        .faq-preview h2 {
            margin-bottom: 30px;
            color: var(--dark-color);
            position: relative;
            display: inline-block;
        }
        
        .faq-preview h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--accent-color);
            border-radius: 2px;
        }
        
        .faq-item {
            background: var(--light-color);
            border-radius: var(--border-radius-sm);
            margin-bottom: 15px;
            overflow: hidden;
            transition: var(--transition);
            border-left: 4px solid var(--accent-color);
        }
        
        .faq-item:hover {
            box-shadow: var(--shadow-sm);
        }
        
        .faq-question {
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            color: var(--dark-color);
            position: relative;
        }
        
        .faq-question::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            color: var(--primary-color);
            transition: var(--transition);
        }
        
        .faq-item.active .faq-question::after {
            transform: rotate(180deg);
        }
        
        .faq-answer {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease;
            padding: 0 20px;
        }
        
        .faq-item.active .faq-answer {
            max-height: 500px;
            padding: 0 20px 15px;
        }
        
        .faq-more {
            text-align: center;
            margin-top: 30px;
        }
        
        .faq-more .btn-outline {
            display: inline-block;
            padding: 10px 25px;
            border: 1px solid var(--accent-color);
            border-radius: 30px;
            color: var(--accent-color);
            font-weight: 500;
            transition: var(--transition);
        }
        
        .faq-more .btn-outline:hover {
            background: var(--accent-color);
            color: #fff;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(30, 86, 160, 0.2);
        }
        
        .support-resources {
            margin-bottom: 60px;
        }
        
        .resources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .resource-card {
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            transition: var(--transition);
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .resource-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow);
        }
        
        .resource-image {
            height: 180px;
            background-color: var(--primary-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 50px;
        }
        
        .resource-content {
            padding: 25px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        
        .resource-content h3 {
            margin-bottom: 15px;
            color: var(--dark-color);
            font-size: 20px;
        }
        
        .resource-content p {
            color: var(--gray-color);
            margin-bottom: 20px;
            flex-grow: 1;
        }
        
        .resource-content .btn-outline {
            align-self: flex-start;
            padding: 8px 20px;
            border: 1px solid var(--accent-color);
            border-radius: 30px;
            color: var(--accent-color);
            font-weight: 500;
            transition: var(--transition);
        }
        
        .resource-content .btn-outline:hover {
            background: var(--accent-color);
            color: #fff;
        }
        
        .shape-animation {
            position: absolute;
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            z-index: 0;
        }
        
        .support-shape-1 {
            top: 10%;
            left: 5%;
            width: 300px;
            height: 300px;
            background-color: rgba(30, 86, 160, 0.05);
            animation: morphShape 15s linear infinite, float 10s ease-in-out infinite;
        }
        
        .support-shape-2 {
            bottom: 10%;
            right: 5%;
            width: 250px;
            height: 250px;
            background-color: rgba(246, 185, 59, 0.05);
            animation: morphShape 20s linear infinite reverse, float 15s ease-in-out infinite;
        }
        
        @keyframes morphShape {
            0% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
            25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
            50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; }
            75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; }
            100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }

/* Support Modals Styles */
.support-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(12, 35, 64, 0.8);
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

.support-modal-content {
    position: relative;
    background-color: #fff;
    margin: 2% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    animation: slideInModal 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInModal {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.support-modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 25px 30px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.support-modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-color);
}

.support-modal-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.support-modal-header h2 i {
    color: var(--accent-color);
    font-size: 1.6rem;
}

.support-close {
    color: white;
    font-size: 32px;
    font-weight: 300;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.support-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.support-modal-body {
    padding: 30px;
}

/* Guides Modal Styles */
.guides-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.guide-item {
    background: #fff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 2px solid #f1f3f5;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.guide-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--accent-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.guide-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
    border-color: var(--accent-color);
}

.guide-item:hover::before {
    transform: scaleX(1);
}

.guide-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: white;
    font-size: 24px;
    transition: all 0.3s ease;
}

.guide-item:hover .guide-icon {
    transform: scale(1.1);
}

.guide-content h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 12px;
}

.guide-content p {
    color: var(--gray-color);
    line-height: 1.6;
    margin-bottom: 20px;
}

.guide-link {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.guide-link:hover {
    color: var(--accent-color);
    transform: translateX(5px);
}

.guide-link i {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.guide-link:hover i {
    transform: translateX(3px);
}

/* Videos Modal Styles */
.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 25px;
}

.video-item {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 2px solid #f1f3f5;
    transition: all 0.3s ease;
    cursor: pointer;
}

.video-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
    border-color: var(--accent-color);
}

.video-thumbnail {
    position: relative;
    height: 180px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 48px;
    transition: all 0.3s ease;
}

.video-thumbnail i {
    transition: all 0.3s ease;
}

.video-item:hover .video-thumbnail i {
    transform: scale(1.2);
    color: var(--accent-color);
}

.video-duration {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.video-content {
    padding: 20px;
}

.video-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 10px;
}

.video-content p {
    color: var(--gray-color);
    line-height: 1.5;
    margin-bottom: 15px;
    font-size: 0.95rem;
}

.video-meta {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    color: var(--gray-color);
}

.video-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.video-meta i {
    color: var(--accent-color);
}

/* Documents Modal Styles */
.documents-categories {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.doc-category {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 2px solid #e9ecef;
}

.doc-category-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.doc-category-header i {
    color: var(--accent-color);
    font-size: 1.5rem;
}

.doc-category-header h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.doc-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.doc-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.doc-item:hover {
    transform: translateX(5px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    border-color: var(--accent-color);
}

.doc-info {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.doc-info i {
    font-size: 1.8rem;
    color: var(--primary-color);
}

.doc-info i.fa-file-pdf {
    color: #dc3545;
}

.doc-info i.fa-file-excel {
    color: #28a745;
}

.doc-info i.fa-file-word {
    color: #007bff;
}

.doc-info h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0 0 5px 0;
}

.doc-info span {
    font-size: 0.85rem;
    color: var(--gray-color);
}

.doc-download {
    background: var(--accent-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.doc-download:hover {
    background: var(--accent-dark);
    transform: scale(1.1);
    color: white;
}

/* Mobile Responsive Styles */
@media screen and (max-width: 768px) {
    .support-modal-content {
        width: 95%;
        margin: 5% auto;
        max-height: 85vh;
    }

    .support-modal-header {
        padding: 20px;
    }

    .support-modal-header h2 {
        font-size: 1.5rem;
    }

    .support-modal-body {
        padding: 20px;
    }

    .guides-grid,
    .videos-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .documents-categories {
        gap: 20px;
    }

    .doc-category {
        padding: 20px;
    }

    .doc-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
        padding: 15px;
    }

    .doc-info {
        width: 100%;
    }

    .doc-download {
        align-self: flex-end;
    }
}

@media screen and (max-width: 480px) {
    .support-modal-content {
        width: 98%;
        margin: 2% auto;
        border-radius: 10px;
    }

    .support-modal-header {
        padding: 15px;
        border-radius: 10px 10px 0 0;
    }

    .support-modal-header h2 {
        font-size: 1.3rem;
    }

    .support-modal-body {
        padding: 15px;
    }

    .guide-item,
    .doc-category {
        padding: 15px;
    }

    .video-thumbnail {
        height: 150px;
        font-size: 36px;
    }

    .video-content {
        padding: 15px;
    }
}
    