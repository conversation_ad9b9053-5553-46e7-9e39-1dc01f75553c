/* Track Claim Page Specific Styles */
:root {
    --primary-color: #1e56a0;
    --primary-dark: #164584;
    --primary-light: #3b7dd4;
    --secondary-color: #163172;
    --accent-color: #f6b93b;
    --accent-dark: #e6a012;
    --dark-color: #0c2340;
    --light-color: #f4f7fc;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --gray-color: #6c757d;
    --gray-light-color: #f8f9fa;
    --shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    --shadow-intense: 0 15px 40px rgba(0, 0, 0, 0.15);
    --shadow-sm: 0 3px 12px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
    --gradient-bg: linear-gradient(135deg, rgba(244, 247, 252, 0.95) 0%, rgba(214, 229, 250, 0.95) 100%);
    
    /* Text colors */
    --text-color: #333;
    --text-light: #6c757d;
    --bg-color: #fff;
    --card-bg: #fff;
}


.claim-hero {
    background: linear-gradient(120deg, var(--accent-color), var(--success-color));


    color: #ffffff;
    padding: 100px 20px;
    text-align: center;
    margin-top: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.claim-hero::before {
    content: "";
    position: absolute;
    width: 400px;
    height: 400px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    top: -100px;
    left: -100px;
    z-index: 0;
}

.claim-hero::after {
    content: "";
    position: absolute;
    width: 300px;
    height: 300px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    bottom: -80px;
    right: -80px;
    z-index: 0;
}

.claim-hero .container {
    position: relative;
    z-index: 1;
    max-width: 900px;
    margin: 0 auto;
}

.claim-hero-content h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    font-weight: 700;
    line-height: 1.2;
}

.claim-hero-content p {
    font-size: 1.2rem;
    color: #cfd8dc;
}

.track-claim-section {
    background-color: var(--light-color);
    position: relative;
    overflow: hidden;
}

.track-claim-container {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

/* .claim-status {
    color: var(--accent-color);
} */

.claim-txt{
    color: var(--accent-color);
}

.track-form {
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 40px;
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
    text-align: center;
}

.track-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--accent-color);
}

.track-form h3 {
    margin-bottom: 20px;
    color: var(--dark-color);
}

.track-form p {
    color: var(--gray-color);
    margin-bottom: 30px;
}

.track-input-group {
    display: flex;
    max-width: 600px;
    margin: 0 auto 20px;
}

.track-input {
    flex: 1;
    padding: 15px 20px;
    border: 1px solid #e1e5ea;
    border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    transition: var(--transition);
}

.track-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 86, 160, 0.1);
}

.track-btn {
    background: var(--accent-color);
    color: #fff;
    border: none;
    padding: 0 30px;
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.track-btn:hover {
    background: var(--accent-color);
}

.track-btn i {
    margin-right: 8px;
}

.track-claim-txt{
    font-size:1rem;
    color: var(--gray-color);
}

.claim-status {
    display: none;
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 40px;
    margin-bottom: 40px;
    position: relative;
}

.claim-status.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.status-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e1e5ea;
}

.status-header-left h3 {
    margin-bottom: 5px;
    color: var(--dark-color);
}

.status-header-left p {
    color: var(--gray-color);
}

.status-badge {
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
}

.status-badge.pending {
    background-color: rgba(255, 193, 7, 0.15);
    color: #d39e00;
}

.status-badge.processing {
    background-color: rgba(0, 123, 255, 0.15);
    color: #0069d9;
}

.status-badge.approved {
    background-color: rgba(40, 167, 69, 0.15);
    color: #218838;
}

.status-badge.rejected {
    background-color: rgba(220, 53, 69, 0.15);
    color: #c82333;
}

.status-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.status-detail-item {
    padding: 15px;
    background-color: var(--light-color);
    border-radius: var(--border-radius-sm);
}

.status-detail-item h4 {
    font-size: 14px;
    color: var(--gray-color);
    margin-bottom: 5px;
}

.status-detail-item p {
    font-weight: 600;
    color: var(--dark-color);
}

.status-timeline {
    margin-top: 40px;
    position: relative;
}

.status-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 15px;
    height: 100%;
    width: 2px;
    background-color: #e1e5ea;
}

.timeline-item {
    position: relative;
    padding-left: 45px;
    padding-bottom: 30px;
}

.timeline-item:last-child {
    padding-bottom: 0;
}

.timeline-dot {
    position: absolute;
    left: 0;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #fff;
    border: 2px solid var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.timeline-dot i {
    color: var(--primary-color);
    font-size: 14px;
}

.timeline-dot.completed {
    background-color: var(--primary-color);
}

.timeline-dot.completed i {
    color: #fff;
}

.timeline-content h4 {
    margin-bottom: 5px;
    color: var(--dark-color);
}

.timeline-content p {
    color: var(--gray-color);
    margin-bottom: 5px;
}

.timeline-date {
    font-size: 14px;
    color: var(--primary-color);
    font-weight: 500;
}

.claim-info-card {
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    padding: 30px;
    margin-bottom: 30px;
    border-left: 4px solid var(--accent-color);
    transition: var(--transition);
}

.claim-info-card:hover {
    box-shadow: var(--shadow);
    transform: translateY(-3px);
}

.claim-info-card h3 {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.claim-info-card h3 i {
    margin-right: 10px;
    color: var(--accent-color);
    font-size: 1.2em;
}

.claim-info-card p {
    color: var(--gray-color);
    margin-bottom: 15px;
}

.claim-info-card ul {
    padding-left: 20px;
}

.claim-info-card ul li {
    margin-bottom: 8px;
    position: relative;
    padding-left: 15px;
}

.claim-info-card ul li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary-color);
}

.shape-animation {
    position: absolute;
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    z-index: 0;
}

.claim-shape-1 {
    top: 10%;
    left: 5%;
    width: 300px;
    height: 300px;
    background-color: rgba(30, 86, 160, 0.05);
    animation: morphShape 15s linear infinite, float 10s ease-in-out infinite;
}

.claim-shape-2 {
    bottom: 10%;
    right: 5%;
    width: 250px;
    height: 250px;
    background-color: rgba(246, 185, 59, 0.05);
    animation: morphShape 20s linear infinite reverse, float 15s ease-in-out infinite;
}

@keyframes morphShape {
    0% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
    25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
    50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; }
    75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; }
    100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
    100% { transform: translateY(0px); }
}

.contact-support {
    text-align: center;
    margin-top: 40px;
    padding: 30px;
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.contact-support h3 {
    margin-bottom: 15px;
    color: var(--dark-color);
}

.contact-support p {
    color: var(--gray-color);
    margin-bottom: 20px;
}

.support-options {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.support-option {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    background-color: var(--accent-color);
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.support-option:hover {
    background-color: var(--accent-color);
    color: #fff;
    transform: translateY(-3px);
    box-shadow: var(--shadow);
}

.support-option:hover i,
.support-option:hover span {
    color: #fff;
}

.support-option i {
    margin-right: 10px;
    color: #fff;
    transition: var(--transition);
}

.support-option span {
    font-weight: 500;
    color: var(--dark-color);
    transition: var(--transition);
}

/* Tablet Devices */
@media (max-width: 992px) {
    .claim-hero-content h1 {
        font-size: 2.8rem;
    }
    .track-claim-container {
        max-width: 95%;
    }
    .track-form, .claim-status {
        padding: 30px 20px;
    }
    .support-options {
        gap: 15px;
    }
}

/* Mobile Devices */
@media (max-width: 768px) {
    .track-input-group {
        flex-direction: column;
    }
    .track-input {
        border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
    }
    .track-btn {
        border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
        padding: 15px;
    }
    .status-header {
        flex-direction: column;
        align-items: flex-start;
    }
    .status-header-right {
        margin-top: 15px;
    }
    .claim-hero-content h1 {
        font-size: 2.5rem;
    }
    .status-details {
        grid-template-columns: 1fr;
    }
    .claim-hero {
        padding: 80px 20px;
    }
    .timeline-item {
        padding-left: 35px;
    }
    .timeline-dot {
        width: 25px;
        height: 25px;
    }
    .support-options {
        flex-direction: column;
        width: 100%;
    }
    .support-option {
        width: 100%;
    }
}

/* Small Mobile Devices */
@media (max-width: 576px) {
    .claim-hero-content h1 {
        font-size: 2rem;
    }
    .claim-hero-content p {
        font-size: 1rem;
    }
    .claim-hero {
        padding: 60px 15px;
    }
    .section-header h2 {
        font-size: 1.8rem;
    }
    .track-form h3, .claim-status h3 {
        font-size: 1.5rem;
    }
    .track-form, .claim-status {
        padding: 25px 15px;
    }
    .status-detail-item {
        padding: 10px;
    }
    .timeline-content h4 {
        font-size: 1rem;
    }
    .timeline-content p {
        font-size: 0.9rem;
    }
}
