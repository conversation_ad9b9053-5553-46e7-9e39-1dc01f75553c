<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64">
  <defs>
    <linearGradient id="person-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2563EB" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>
    <linearGradient id="connection-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#4F46E5" />
      <stop offset="100%" stop-color="#8B5CF6" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="32" cy="32" r="30" fill="#F3F4F6" opacity="0.7" />
  
  <!-- Central Person -->
  <g transform="translate(32, 32)">
    <!-- Head -->
    <circle cx="0" cy="-3" r="5" fill="url(#person-gradient)">
      <animate attributeName="opacity" values="0;1" dur="0.5s" begin="0.2s" fill="freeze" />
    </circle>
    
    <!-- Body -->
    <path d="M-3,-1 C-5,0 -5,6 -3,8 C-1,10 1,10 3,8 C5,6 5,0 3,-1 Z" fill="url(#person-gradient)" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.5s" begin="0.4s" fill="freeze" />
    </path>
  </g>
  
  <!-- Surrounding People -->
  <!-- Person 1 (Top) -->
  <g transform="translate(32, 10)">
    <circle cx="0" cy="0" r="3" fill="url(#person-gradient)" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="0.6s" fill="freeze" />
    </circle>
    <path d="M-2,1 C-3,2 -3,5 -2,6 C-1,7 1,7 2,6 C3,5 3,2 2,1 Z" fill="url(#person-gradient)" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="0.7s" fill="freeze" />
    </path>
  </g>
  
  <!-- Person 2 (Right) -->
  <g transform="translate(50, 32)">
    <circle cx="0" cy="0" r="3" fill="url(#person-gradient)" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="0.8s" fill="freeze" />
    </circle>
    <path d="M-2,1 C-3,2 -3,5 -2,6 C-1,7 1,7 2,6 C3,5 3,2 2,1 Z" fill="url(#person-gradient)" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="0.9s" fill="freeze" />
    </path>
  </g>
  
  <!-- Person 3 (Bottom) -->
  <g transform="translate(32, 54)">
    <circle cx="0" cy="0" r="3" fill="url(#person-gradient)" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="1.0s" fill="freeze" />
    </circle>
    <path d="M-2,1 C-3,2 -3,5 -2,6 C-1,7 1,7 2,6 C3,5 3,2 2,1 Z" fill="url(#person-gradient)" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="1.1s" fill="freeze" />
    </path>
  </g>
  
  <!-- Person 4 (Left) -->
  <g transform="translate(14, 32)">
    <circle cx="0" cy="0" r="3" fill="url(#person-gradient)" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="1.2s" fill="freeze" />
    </circle>
    <path d="M-2,1 C-3,2 -3,5 -2,6 C-1,7 1,7 2,6 C3,5 3,2 2,1 Z" fill="url(#person-gradient)" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="1.3s" fill="freeze" />
    </path>
  </g>
  
  <!-- Connections -->
  <!-- Connection 1 (Center to Top) -->
  <line x1="32" y1="24" x2="32" y2="17" stroke="url(#connection-gradient)" stroke-width="1.5" stroke-dasharray="7" stroke-dashoffset="7">
    <animate attributeName="stroke-dashoffset" values="7;0" dur="0.4s" begin="1.4s" fill="freeze" />
  </line>
  
  <!-- Connection 2 (Center to Right) -->
  <line x1="40" y1="32" x2="47" y2="32" stroke="url(#connection-gradient)" stroke-width="1.5" stroke-dasharray="7" stroke-dashoffset="7">
    <animate attributeName="stroke-dashoffset" values="7;0" dur="0.4s" begin="1.5s" fill="freeze" />
  </line>
  
  <!-- Connection 3 (Center to Bottom) -->
  <line x1="32" y1="40" x2="32" y2="47" stroke="url(#connection-gradient)" stroke-width="1.5" stroke-dasharray="7" stroke-dashoffset="7">
    <animate attributeName="stroke-dashoffset" values="7;0" dur="0.4s" begin="1.6s" fill="freeze" />
  </line>
  
  <!-- Connection 4 (Center to Left) -->
  <line x1="24" y1="32" x2="17" y2="32" stroke="url(#connection-gradient)" stroke-width="1.5" stroke-dasharray="7" stroke-dashoffset="7">
    <animate attributeName="stroke-dashoffset" values="7;0" dur="0.4s" begin="1.7s" fill="freeze" />
  </line>
  
  <!-- Connection pulses -->
  <circle cx="32" cy="32" r="12" fill="none" stroke="url(#connection-gradient)" stroke-width="1" opacity="0">
    <animate attributeName="r" values="12;20" dur="1.5s" begin="1.8s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.6;0" dur="1.5s" begin="1.8s" repeatCount="indefinite" />
  </circle>
  
  <circle cx="32" cy="32" r="12" fill="none" stroke="url(#connection-gradient)" stroke-width="1" opacity="0">
    <animate attributeName="r" values="12;20" dur="1.5s" begin="2.3s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.6;0" dur="1.5s" begin="2.3s" repeatCount="indefinite" />
  </circle>
  
  <!-- Collaboration Symbols -->
  <g transform="translate(32, 32)">
    <path d="M0,-15 L3,-10 L8,-12 L5,-7 L9,-3 L3,-3 L0,2 L-3,-3 L-9,-3 L-5,-7 L-8,-12 L-3,-10 Z" 
          fill="none" stroke="#8B5CF6" stroke-width="1" opacity="0">
      <animate attributeName="opacity" values="0;0.2" dur="0.5s" begin="2.0s" fill="freeze" />
    </path>
  </g>
</svg> 