<svg xmlns="http://www.w3.org/2000/svg" width="500" height="250" viewBox="0 0 500 250">
  <style>
    .chart-bg { fill: white; }
    .grid-line { stroke: #E5E7EB; stroke-width: 1; }
    .axis-label { font-family: 'Arial', sans-serif; font-size: 12px; fill: #94A3B8; }
    .bar { fill: #F6B93B; opacity: 0.8; rx: 4; ry: 4; }
    .bar:hover { opacity: 1; }
    .line { fill: none; stroke: #1E56A0; stroke-width: 3; stroke-linecap: round; stroke-linejoin: round; }
    .area { fill: url(#gradient); opacity: 0.2; }
    .dot { fill: #1E56A0; stroke: white; stroke-width: 2; r: 6; }
    .dot:hover { r: 8; }
    .annotation { font-family: 'Arial', sans-serif; font-size: 11px; font-weight: bold; fill: #1E56A0; }
    .animate-in { animation: fadeIn 1s ease-out forwards; }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
  </style>
  
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#1E56A0" stop-opacity="0.5" />
      <stop offset="100%" stop-color="#1E56A0" stop-opacity="0" />
    </linearGradient>
  </defs>
  
  <rect class="chart-bg" x="0" y="0" width="500" height="250" />
  
  <!-- Grid lines -->
  <line class="grid-line" x1="50" y1="200" x2="450" y2="200" />
  <line class="grid-line" x1="50" y1="150" x2="450" y2="150" />
  <line class="grid-line" x1="50" y1="100" x2="450" y2="100" />
  <line class="grid-line" x1="50" y1="50" x2="450" y2="50" />
  
  <!-- X axis labels -->
  <text class="axis-label" x="80" y="220">Q1</text>
  <text class="axis-label" x="150" y="220">Q2</text>
  <text class="axis-label" x="220" y="220">Q3</text>
  <text class="axis-label" x="290" y="220">Q4</text>
  <text class="axis-label" x="360" y="220">Q1</text>
  <text class="axis-label" x="430" y="220">Q2</text>
  
  <!-- Y axis labels -->
  <text class="axis-label" x="30" y="205">0</text>
  <text class="axis-label" x="30" y="155">25</text>
  <text class="axis-label" x="30" y="105">50</text>
  <text class="axis-label" x="30" y="55">75</text>
  
  <!-- Bars -->
  <rect class="bar" x="65" y="170" width="30" height="30" style="animation-delay: 0.1s" />
  <rect class="bar" x="135" y="160" width="30" height="40" style="animation-delay: 0.2s" />
  <rect class="bar" x="205" y="140" width="30" height="60" style="animation-delay: 0.3s" />
  <rect class="bar" x="275" y="120" width="30" height="80" style="animation-delay: 0.4s" />
  <rect class="bar" x="345" y="110" width="30" height="90" style="animation-delay: 0.5s" />
  <rect class="bar" x="415" y="90" width="30" height="110" style="animation-delay: 0.6s" />
  
  <!-- Area chart -->
  <path class="area" d="M80 180 L150 160 L220 130 L290 120 L360 90 L430 60 L430 200 L80 200 Z" />
  
  <!-- Line chart -->
  <path class="line" d="M80 180 L150 160 L220 130 L290 120 L360 90 L430 60" />
  
  <!-- Data points -->
  <circle class="dot" cx="80" cy="180" style="animation-delay: 0.7s" />
  <circle class="dot" cx="150" cy="160" style="animation-delay: 0.8s" />
  <circle class="dot" cx="220" cy="130" style="animation-delay: 0.9s" />
  <circle class="dot" cx="290" cy="120" style="animation-delay: 1.0s" />
  <circle class="dot" cx="360" cy="90" style="animation-delay: 1.1s" />
  <circle class="dot" cx="430" cy="60" style="animation-delay: 1.2s" />
  
  <!-- Annotations -->
  <text class="annotation" x="440" y="50">+28%</text>
  <path d="M440 55 L450 65" stroke="#1E56A0" stroke-width="1.5" />
</svg> 