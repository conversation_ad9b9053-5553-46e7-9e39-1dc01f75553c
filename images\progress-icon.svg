<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64">
  <defs>
    <linearGradient id="chart-gradient" x1="0%" y1="100%" x2="0%" y2="0%">
      <stop offset="0%" stop-color="#1D4ED8" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>
    <linearGradient id="success-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#15803D" />
      <stop offset="100%" stop-color="#22C55E" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="32" cy="32" r="28" fill="#F3F4F6" stroke="#E5E7EB" stroke-width="1" />
  
  <!-- Progress Arc Backgrounds -->
  <circle cx="32" cy="32" r="22" fill="none" stroke="#E5E7EB" stroke-width="6" stroke-linecap="round" />
  
  <!-- Progress Arcs -->
  <!-- First Arc (75% complete) -->
  <path d="M32,10 A22,22 0 1,1 10,32" fill="none" stroke="url(#chart-gradient)" stroke-width="6" 
        stroke-linecap="round" stroke-dasharray="138" stroke-dashoffset="138">
    <animate attributeName="stroke-dashoffset" from="138" to="34.5" dur="1.5s" begin="0.2s" fill="freeze" />
  </path>
  
  <!-- Second Arc (90% complete) -->
  <path d="M32,10 A22,22 0 1,1 10,32" fill="none" stroke="#3B82F6" stroke-width="3" 
        stroke-linecap="round" stroke-dasharray="138" stroke-dashoffset="138" transform="rotate(90 32 32)">
    <animate attributeName="stroke-dashoffset" from="138" to="13.8" dur="1.5s" begin="0.5s" fill="freeze" />
  </path>
  
  <!-- Central Content -->
  <circle cx="32" cy="32" r="15" fill="white" stroke="#E5E7EB" stroke-width="1" />
  
  <!-- Achievement Star -->
  <g transform="translate(32,32) scale(0)">
    <animate attributeName="transform" from="translate(32,32) scale(0)" to="translate(32,32) scale(1)" 
             dur="0.5s" begin="1.7s" fill="freeze" />
    
    <!-- Star Shape -->
    <path d="M0,-9 L2,-3 L8,-3 L3,1 L5,7 L0,3 L-5,7 L-3,1 L-8,-3 L-2,-3 Z" 
          fill="url(#success-gradient)" />
    
    <!-- Center Point -->
    <circle cx="0" cy="0" r="2" fill="#FBBF24" />
  </g>
  
  <!-- Percentage Text -->
  <text x="32" y="40" font-family="Arial" font-size="7" font-weight="bold" text-anchor="middle" fill="#1E40AF" opacity="0">
    95%
    <animate attributeName="opacity" from="0" to="1" dur="0.3s" begin="2s" fill="freeze" />
  </text>
  
  <!-- Mini Charts in Background -->
  <!-- Bar Chart -->
  <g transform="translate(15,50) scale(0.6)" opacity="0.6">
    <rect x="0" y="0" width="3" height="8" fill="#3B82F6" />
    <rect x="5" y="-3" width="3" height="11" fill="#3B82F6" />
    <rect x="10" y="-5" width="3" height="13" fill="#3B82F6" />
    <rect x="15" y="-2" width="3" height="10" fill="#3B82F6" />
  </g>
  
  <!-- Line Chart -->
  <path d="M40,51 L45,48 L50,50 L55,45" fill="none" stroke="#22C55E" stroke-width="1.5" 
        stroke-dasharray="20" stroke-dashoffset="20">
    <animate attributeName="stroke-dashoffset" from="20" to="0" dur="0.8s" begin="1.2s" fill="freeze" />
  </path>
  
  <!-- Checkmark for Completion -->
  <path d="M48,20 L52,24 L58,18" fill="none" stroke="#22C55E" stroke-width="2" stroke-linecap="round"
        stroke-dasharray="15" stroke-dashoffset="15">
    <animate attributeName="stroke-dashoffset" from="15" to="0" dur="0.5s" begin="2.2s" fill="freeze" />
  </path>
</svg> 