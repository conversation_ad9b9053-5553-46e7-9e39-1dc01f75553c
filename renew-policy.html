<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Renew Your Policy Online | Growwell Insurance</title>
    <meta
      name="description"
      content="Renew your financial services or investment plans with Growwell. Continue your journey toward financial security with seamless plan renewal options."
    />
    <meta
      name="keywords"
      content="growwell, investment plans, policy renewal, term insurance"
    />
    <meta name="robots" content="renew-policy, follow" />
    <link rel="canonical" href="https://growwellimf.com/" />
    <meta
      property="og:title"
      content="Renew Your Policy Online | Growwell Insurance"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://growwellimf.com/" />
    <meta
      property="og:image"
      content="https://growwellimf.com/images/growwell_logo_01.webp"
    />
    <meta name="author" content="Growwell" />
    <meta name="publisher" content="Growwell" />

    <!--google adsense-->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>

    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/renew-policy.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Header Section -->
    <header>
      <div class="container">
        <div class="navbar">
          <div class="logo">
            <a href="index.html">
              <img
                src="./images/growwell_logo_01.webp"
                alt="company_logo"
                class="brand-logo"
            /></a>
            <a href="index.html"
              ><h2><span class="highlight">Grow</span>well</h2></a
            >
          </div>

          <div class="hamburger" id="hamburger">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
          </div>

          <nav id="nav-menu">
            <ul id="menu-items">
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Products</a>
                <div class="dropdown-menu">
                  <a href="investment-plans.html">Investment Plans</a>
                  <a href="retirement-plans.html">Retirement Plans</a>
                  <a href="child-plans.html">Child Plans</a>
                  <a href="term-insurance.html">Term Insurance</a>
                  <a href="tax-advisory.html">Tax Advisory</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Renew</a>
                <div class="dropdown-menu">
                  <a href="renew-policy.html">Renew Policy</a>
                  <a href="check-status.html">Check Status</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Claim</a>
                <div class="dropdown-menu">
                  <a href="file-claim.html">File a Claim</a>
                  <a href="track-claim.html">Track Claim</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Help & Support</a>
                <div class="dropdown-menu">
                  <a href="faqs.html">FAQs</a>
                  <a href="contact.html">Contact Us</a>
                  <a href="support.html">Customer Support</a>
                  <a href="advisor.html">Find an Advisor</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">About Us</a>
                <div class="dropdown-menu">
                  <a href="our-story.html">Our Story</a>
                  <a href="leadership.html">Leadership</a>
                  <a href="careers.html">Careers</a>
                </div>
              </li>
            </ul>
          </nav>

          <div class="navbar-actions desktop-only">
            <a href="investment-plans.html" class="btn-primary">Get Started</a>
          </div>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content-wrapper">
          <div class="hero-content">
            <span class="hero-badge">Quick & Easy</span>
            <h1>
              Simple & <span class="gradient-text">Hassle-Free</span
              ><br />Policy Renewal
            </h1>
            <p>
              Renew your insurance policy in minutes and continue enjoying
              comprehensive coverage and peace of mind. Secure your future with
              just a few clicks.
            </p>
            <div class="hero-buttons">
              <a href="#renewal-form" class="renew-btn"
                >Renew Now <i class="fas fa-arrow-right"></i
              ></a>
              <a href="#renewal-process" class="btn-secondary"
                >Learn How <i class="fas fa-info-circle"></i
              ></a>
            </div>
            <div class="hero-features">
              <div class="hero-feature">
                <i class="fas fa-bolt"></i>
                <span>5-Minute Process</span>
              </div>
              <div class="hero-feature">
                <i class="fas fa-lock"></i>
                <span>Secure Payment</span>
              </div>
              <div class="hero-feature">
                <i class="fas fa-file-alt"></i>
                <span>Instant Documentation</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="hero-bg-shape"></div>
    </section>

    <!-- Benefits Section -->
    <section class="benefits-section">
      <div class="container">
        <div class="section-header">
          <span class="section-badge">Why Renew</span>
          <h2 class="section-title">
            Benefits of Renewing Your <span class="policy-txt"> Policy</span>
          </h2>
          <p class="section-subtitle">
            Keep your coverage active and enjoy these exclusive advantages
          </p>
        </div>
        <div class="benefits-grid">
          <div class="benefit-card">
            <div class="benefit-card-inner">
              <div class="benefit-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <h3>Continuous Protection</h3>
              <p>
                No gaps in coverage means uninterrupted protection for you and
                your loved ones.
              </p>
              <div class="benefit-indicator">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
          </div>
          <div class="benefit-card featured">
            <div class="benefit-card-inner">
              <div class="benefit-tag">Popular</div>
              <div class="benefit-icon">
                <i class="fas fa-percent"></i>
              </div>
              <h3>Loyalty Discounts</h3>
              <p>
                Enjoy special discounts and offers available only for loyal
                returning customers.
              </p>
              <div class="benefit-indicator">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
          </div>
          <div class="benefit-card">
            <div class="benefit-card-inner">
              <div class="benefit-icon">
                <i class="fas fa-clock"></i>
              </div>
              <h3>No Waiting Period</h3>
              <p>
                Avoid new waiting periods that would apply if you let your
                policy lapse.
              </p>
              <div class="benefit-indicator">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
          </div>
          <div class="benefit-card">
            <div class="benefit-card-inner">
              <div class="benefit-icon">
                <i class="fas fa-star"></i>
              </div>
              <h3>Enhanced Benefits</h3>
              <p>
                Access to new and improved benefits we've added since your
                original purchase.
              </p>
              <div class="benefit-indicator">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="benefits-footer">
          <a href="#renewal-form" class="benefits-cta"
            >Renew Your Policy Now <i class="fas fa-long-arrow-alt-right"></i
          ></a>
        </div>
      </div>
      <div class="benefits-shape benefits-shape-1"></div>
      <div class="benefits-shape benefits-shape-2"></div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
      <div class="container">
        <div class="section-header">
          <span class="section-badge">Our Impact</span>
          <h2 class="section-title">
            Why Customers <span class="trust-txt">Trust Us</span>
          </h2>
          <p class="section-subtitle">
            Join thousands of satisfied customers who've experienced our
            seamless renewal process
          </p>
        </div>
        <div class="stats-container">
          <div class="stat-item">
            <div class="stat-icon">
              <i class="fas fa-thumbs-up"></i>
            </div>
            <div class="stat-number" data-value="98">
              0<span class="stat-symbol">%</span>
            </div>
            <div class="stat-label">Renewal Satisfaction</div>
            <div class="stat-description">
              Our customers rate their renewal experience as excellent
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <i class="fas fa-bolt"></i>
            </div>
            <div class="stat-number" data-value="5">
              0<span class="stat-symbol">min</span>
            </div>
            <div class="stat-label">Average Renewal Time</div>
            <div class="stat-description">
              Quick and hassle-free process that respects your time
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="stat-number" data-value="2000000">
              0<span class="stat-symbol">+</span>
            </div>
            <div class="stat-label">Happy Customers</div>
            <div class="stat-description">
              Millions trust us with their insurance needs
            </div>
          </div>
        </div>
      </div>
      <div class="stats-shape stats-shape-1"></div>
      <div class="stats-shape stats-shape-2"></div>
      <div class="stats-shape stats-shape-3"></div>
    </section>

    <!-- Renewal Process Section -->
    <section id="renewal-process" class="renewal-process-section">
      <div class="container">
        <div class="section-header">
          <span class="section-badge">How It Works</span>
          <h2 class="section-title">
            Simple 3-Step <span class="policy-txt"> Renewal Process</span>
          </h2>
          <p class="section-subtitle">
            Renew your policy in minutes with our streamlined process
          </p>
        </div>

        <div class="process-cards-container">
          <div class="process-card">
            <div class="process-card-icon">
              <div class="circle-icon">
                <i class="fas fa-user-edit"></i>
              </div>
              <div class="step-number">1</div>
            </div>
            <div class="process-card-content">
              <h3>Enter Your Details</h3>
              <p>
                Fill in your policy number and registered mobile number or email
                ID to begin the renewal process.
              </p>
              <ul class="process-card-features">
                <li>
                  <i class="fas fa-check-circle"></i> Policy number verification
                </li>
                <li>
                  <i class="fas fa-check-circle"></i> Secure identification
                </li>
                <li>
                  <i class="fas fa-check-circle"></i> Quick information
                  retrieval
                </li>
              </ul>
            </div>
          </div>

          <div class="process-card">
            <div class="process-card-icon">
              <div class="circle-icon">
                <i class="fas fa-clipboard-check"></i>
              </div>
              <div class="step-number">2</div>
            </div>
            <div class="process-card-content">
              <h3>Review & Choose</h3>
              <p>
                Review your policy details and select your preferred payment
                option for a seamless experience.
              </p>
              <ul class="process-card-features">
                <li>
                  <i class="fas fa-check-circle"></i> Verify coverage details
                </li>
                <li>
                  <i class="fas fa-check-circle"></i> Flexible payment options
                </li>
                <li>
                  <i class="fas fa-check-circle"></i> Option to modify coverage
                </li>
              </ul>
            </div>
          </div>

          <div class="process-card">
            <div class="process-card-icon">
              <div class="circle-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <div class="step-number">3</div>
            </div>
            <div class="process-card-content">
              <h3>Make Payment</h3>
              <p>
                Complete a secure payment and receive instant confirmation of
                your policy renewal.
              </p>
              <ul class="process-card-features">
                <li>
                  <i class="fas fa-check-circle"></i> 128-bit encrypted payment
                </li>
                <li>
                  <i class="fas fa-check-circle"></i> Instant digital receipt
                </li>
                <li>
                  <i class="fas fa-check-circle"></i> Immediate coverage
                  activation
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="process-shape process-shape-1"></div>
      <div class="process-shape process-shape-2"></div>
    </section>

    <!-- Renewal Form Section -->
    <section id="renewal-form" class="renewal-form-section">
      <div class="container">
        <h2 class="section-title">
          Renew Your <span class="policy-txt"> Policy</span>
        </h2>
        <div class="form-container">
          <div class="form-tabs">
            <div class="form-tab active" data-target="tab-policy">
              Policy Number
            </div>
            <div class="form-tab" data-target="tab-mobile">Mobile Number</div>
            <div class="form-tab" data-target="tab-email">Email ID</div>
          </div>

          <div class="tab-content">
            <!-- Policy Number Tab -->
            <div id="tab-policy" class="tab-pane active">
              <form class="renew-form">
                <div class="form-group">
                  <label for="policy_number">Policy Number</label>
                  <input
                    type="text"
                    id="policy_number"
                    name="policy_number"
                    placeholder="Enter your policy number"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="dob_policy">Date of Birth</label>
                  <input type="date" id="dob_policy" name="dob" required />
                </div>
                <div class="form-actions">
                  <button type="submit" class="btn-primary btn-large">
                    Proceed
                  </button>
                </div>
              </form>
            </div>

            <!-- Mobile Number Tab -->
            <div id="tab-mobile" class="tab-pane">
              <form class="renew-form">
                <div class="form-group">
                  <label for="mobile_number">Mobile Number</label>
                  <div class="input-group">
                    <input
                      type="tel"
                      id="mobile_number"
                      name="mobile"
                      placeholder="Enter your 10-digit mobile number"
                      required
                    />
                    <button
                      type="button"
                      class="btn-otp"
                      data-target="mobile_number"
                      data-otp-field="otp_container"
                    >
                      Get OTP
                    </button>
                  </div>
                </div>
                <div
                  id="otp_container"
                  class="form-group"
                  style="display: none"
                >
                  <label for="otp_input">OTP</label>
                  <input
                    type="text"
                    id="otp_input"
                    name="otp"
                    placeholder="Enter the 6-digit OTP"
                    maxlength="6"
                  />
                </div>
                <div class="form-actions">
                  <button type="submit" class="btn-primary">Proceed</button>
                </div>
              </form>
            </div>

            <!-- Email Tab -->
            <div id="tab-email" class="tab-pane">
              <form class="renew-form">
                <div class="form-group">
                  <label for="email_id">Email ID</label>
                  <input
                    type="email"
                    id="email_id"
                    name="email"
                    placeholder="Enter your registered email ID"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="dob_email">Date of Birth</label>
                  <input type="date" id="dob_email" name="dob" required />
                </div>
                <div class="form-actions">
                  <button type="submit" class="btn-primary">Proceed</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
      <div class="container">
        <h2 class="section-title">
          Frequently Asked <span class="policy-txt"> Questions</span>
        </h2>
        <div class="faq-container">
          <div class="faq-item">
            <div class="faq-question">
              <h3>When should I renew my policy?</h3>
              <div class="faq-icon">
                <i class="fas fa-chevron-down"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>
                We recommend renewing your policy at least 15 days before your
                current policy expires. This ensures there are no gaps in your
                coverage. You'll receive renewal notifications 45 days, 30 days,
                and 15 days before expiry.
              </p>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <h3>What happens if I miss the renewal date?</h3>
              <div class="faq-icon">
                <i class="fas fa-chevron-down"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>
                If you miss your renewal date, your policy lapses and you lose
                coverage. Some policies offer a grace period (typically 15-30
                days) where you can still renew without losing benefits. Beyond
                that, you may need to go through a new application process,
                medical examinations, and waiting periods.
              </p>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <h3>Can I modify my coverage during renewal?</h3>
              <div class="faq-icon">
                <i class="fas fa-chevron-down"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>
                Yes, renewal time is an ideal opportunity to review and update
                your coverage. You can increase or decrease sum insured, add or
                remove family members, or add riders for additional protection.
                Some changes may require additional documentation or premium
                adjustments.
              </p>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <h3>Will my premium increase upon renewal?</h3>
              <div class="faq-icon">
                <i class="fas fa-chevron-down"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>
                Premium adjustments may occur at renewal based on factors like
                age, health conditions, claims history, and inflation. We strive
                to keep increases minimal and offer loyalty discounts to
                long-term customers. Any premium changes will be clearly
                communicated in your renewal notice.
              </p>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <h3>How do I get my renewal documents?</h3>
              <div class="faq-icon">
                <i class="fas fa-chevron-down"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>
                After successful renewal, all policy documents will be emailed
                to your registered email address within 24 hours. You can also
                download them from your online account or request physical
                copies that will be delivered to your registered address within
                7-10 business days.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Support Section -->
    <section class="contact-support-section">
      <div class="container">
        <h2 class="section-title">
          Need Help with Your <span class="policy-txt"> Renewal?</span>
        </h2>
        <p class="section-subtitle">
          Our dedicated support team is here to assist you with any questions or
          concerns.
        </p>

        <div class="support-options">
          <div class="support-option">
            <div class="support-icon">
              <i class="fas fa-phone-alt"></i>
            </div>
            <h3>Call Us</h3>
            <p>Speak to our customer service representatives</p>
            <a href="tel:*************" class="support-link"
              >1800-123-456-789</a
            >
            <p class="support-timing">Mon-Sat: 8am-8pm</p>
          </div>

          <div class="support-option">
            <div class="support-icon">
              <i class="fas fa-comment-dots"></i>
            </div>
            <h3>Live Chat</h3>
            <p>Chat with our renewal specialists</p>
            <a href="#" class="btn-chat support-link">Start Chat</a>
            <p class="support-timing">Available 24/7</p>
          </div>

          <div class="support-option">
            <div class="support-icon">
              <i class="fas fa-envelope"></i>
            </div>
            <h3>Email Us</h3>
            <p>Send us your queries</p>
            <a href="mailto:<EMAIL>" class="support-link"
              ><EMAIL></a
            >
            <p class="support-timing">Response within 24 hours</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
      <div class="container">
        <div class="cta-content">
          <span class="section-subtitle">Get Started Today</span>
          <h2>Ready to Secure Your Financial Future?</h2>
          <p>
            Schedule a free consultation with one of our expert financial
            advisors today.
          </p>
          <div class="cta-buttons">
            <a href="investment-plans.html" class="btn btn-primary">
              Schedule Consultation <i class="fas fa-calendar-alt"></i
            ></a>
            <a href="tel:+15551234567" class="btn btn-secondary">
              Call Us Now <i class="fas fa-phone-alt"></i
            ></a>
          </div>
          <div class="trust-indicators">
            <div class="trust-item">
              <i class="fas fa-check-circle"></i>
              <span>Free Consultation</span>
            </div>
            <div class="trust-item">
              <i class="fas fa-check-circle"></i>
              <span>Expert Advisors</span>
            </div>
            <div class="trust-item">
              <i class="fas fa-check-circle"></i>
              <span>Personalized Plans</span>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Footer -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <div class="footer-logo-img">
              <img
                src="./images/growwell_logo_01.webp"
                alt="Growwell Logo"
                loading="lazy"
                class="footer-logo-image"
              />
            </div>
            <p>Your partner for financial growth and security</p>
            <div class="social-icons">
              <a
                href="https://www.facebook.com/growwellimf/"
                target="/"
                aria-label="Follow us on Facebook"
                ><i class="fab fa-facebook-f"></i
              ></a>
              <a
                href="https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08"
                target="/"
                aria-label="Follow us on X"
                ><i class="fab fa-twitter"></i
              ></a>
              <a
                href="https://www.linkedin.com/company/grow-well-imf/about/"
                target="/"
                aria-label="Follow us on Linkedin"
                ><i class="fab fa-linkedin-in"></i
              ></a>
              <a
                href="https://www.instagram.com/growwell.imf/"
                target="/"
                aria-label="Follow us on Instagram"
                ><i class="fab fa-instagram"></i
              ></a>
            </div>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h3>Quick Links</h3>
              <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="./investment-plans.html">Products</a></li>
                <li><a href="./renew-policy.html">Renew</a></li>
                <li><a href="./advisor.html">Testimonials</a></li>
                <li><a href="./contact.html">Contact</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Services</h3>
              <ul>
                <li><a href="investment-plans.html">Financial Planning</a></li>
                <li><a href="child-plans.html">Child Planning</a></li>
                <li><a href="investment-plans.html">Investment Planning</a></li>
                <li><a href="retirement-plans.html">Retirement Planning</a></li>
                <li><a href="term-insurance.html">Term Insurance</a></li>
                <li><a href="#">Tax Advisory</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Resources</h3>
              <ul>
                <li><a href="leadership.html">Leadership</a></li>
                <li><a href="advisor.html">Advisor</a></li>
                <li>
                  <a href="retirement-plans.html">Financial Calculators</a>
                </li>
                <li><a href="faqs.html">FAQs</a></li>
                <li><a href="contact.html">contact</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2023 Growwell Financial Advisors. All rights reserved.</p>
          <ul class="footer-legal">
            <li><a href="privacy-policy.html">Privacy Policy</a></li>
            <li><a href="terms-of-service.html">Terms of Service</a></li>
            <li><a href="cookie-policy.html">Cookie Policy</a></li>
          </ul>
        </div>
      </div>
    </footer>

    <script src="js/renew-policy.js"></script>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Growwell",
        "url": "https://growwellimf.com",
        "logo": "https://growwellimf.com/images/growwell_logo_01.webp",
        "sameAs": [
          "https://www.facebook.com/growwellimf/",
          "https://www.instagram.com/growwell.imf/",
          "https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08",
          "https://www.linkedin.com/company/grow-well-imf/about/"
        ]
      }
    </script>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Mobile Menu Toggle
        const hamburger = document.getElementById("hamburger");
        const navMenu = document.getElementById("nav-menu");
        const menuItems = document.getElementById("menu-items");
        const body = document.body;

        if (hamburger) {
          hamburger.addEventListener("click", function () {
            // Toggle navigation menu
            navMenu.classList.toggle("active");

            // Toggle body class to prevent scrolling
            body.classList.toggle("nav-active");

            // Toggle hamburger icon animation
            const bars = this.querySelectorAll(".bar");
            bars.forEach((bar) => bar.classList.toggle("active"));

            // Log for debugging
            console.log(
              "Hamburger clicked. Nav active:",
              navMenu.classList.contains("active")
            );
          });
        }

        // Dropdown Toggle for Mobile
        const dropdownToggles = document.querySelectorAll(".dropdown-toggle");
        dropdownToggles.forEach((toggle) => {
          toggle.addEventListener("click", function (e) {
            // Only run on mobile (width <= 992px)
            if (window.innerWidth <= 992) {
              e.preventDefault();
              e.stopPropagation();

              // Find the dropdown menu that is a sibling of this toggle
              const dropdownMenu = this.nextElementSibling;

              // Close all other dropdown menus
              document.querySelectorAll(".dropdown-menu").forEach((menu) => {
                if (menu !== dropdownMenu) {
                  menu.classList.remove("active");
                }
              });

              // Remove active class from all other toggles
              document.querySelectorAll(".dropdown-toggle").forEach((tog) => {
                if (tog !== this) {
                  tog.classList.remove("active");
                }
              });

              // Toggle active class on this dropdown menu and toggle
              dropdownMenu.classList.toggle("active");
              this.classList.toggle("active");

              // Log for debugging
              console.log(
                "Dropdown toggled:",
                this.textContent.trim(),
                "Active:",
                dropdownMenu.classList.contains("active")
              );
            }
          });
        });

        // Close menu when clicking outside
        document.addEventListener("click", function (e) {
          if (window.innerWidth <= 992) {
            if (!e.target.closest("nav") && !e.target.closest("#hamburger")) {
              // Close the navigation menu
              if (navMenu.classList.contains("active")) {
                navMenu.classList.remove("active");
                body.classList.remove("nav-active");

                // Reset hamburger icon
                const bars = hamburger.querySelectorAll(".bar");
                bars.forEach((bar) => bar.classList.remove("active"));

                // Close all dropdown menus
                document.querySelectorAll(".dropdown-menu").forEach((menu) => {
                  menu.classList.remove("active");
                });

                // Remove active class from all toggles
                document.querySelectorAll(".dropdown-toggle").forEach((tog) => {
                  tog.classList.remove("active");
                });
              }
            }
          }
        });

        // Initialize form tabs
        const formTabs = document.querySelectorAll(".form-tab");
        formTabs.forEach((tab) => {
          tab.addEventListener("click", function () {
            // Remove active class from all tabs
            formTabs.forEach((t) => t.classList.remove("active"));

            // Add active class to clicked tab
            this.classList.add("active");

            // Hide all tab panes
            document.querySelectorAll(".tab-pane").forEach((pane) => {
              pane.classList.remove("active");
            });

            // Show the selected tab pane
            const target = this.getAttribute("data-target");
            document.getElementById(target).classList.add("active");
          });
        });

        // FAQ accordion
        const faqQuestions = document.querySelectorAll(".faq-question");
        faqQuestions.forEach((question) => {
          question.addEventListener("click", function () {
            const faqItem = this.parentElement;
            faqItem.classList.toggle("active");
          });
        });

        document.querySelectorAll(".faq-question").forEach((question) => {
          question.addEventListener("click", () => {
            const item = question.parentElement;
            const answer = item.querySelector(".faq-answer");
            const icon = question.querySelector(".faq-icon i");

            // Toggle active class
            item.classList.toggle("active");

            // Toggle answer visibility
            if (item.classList.contains("active")) {
              answer.style.maxHeight = answer.scrollHeight + "px";
              icon.classList.add("rotate");
            } else {
              answer.style.maxHeight = null;
              icon.classList.remove("rotate");
            }
          });
        });

        // Set initial maxHeight for active items (if needed)
        document
          .querySelectorAll(".faq-item.active .faq-answer")
          .forEach((answer) => {
            answer.style.maxHeight = answer.scrollHeight + "px";
          });
      });
    </script>
  </body>
</html>
