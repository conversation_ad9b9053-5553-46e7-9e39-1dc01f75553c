<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Secure Retirement Plans for Future | Growwell</title>
    <meta
      name="description"
      content="Plan your retirement with confidence. Growwell offers expert retirement solutions to help you secure a financially independent and stress-free future."
    />
    <meta
      name="keywords"
      content="growwell, retirement plans, retirement solutions, retirement planning, retirement calculator"
    />
    <meta name="robots" content="retirement-plans, follow" />
    <link rel="canonical" href="https://growwellimf.com/" />
    <meta
      property="og:title"
      content="Secure Retirement Plans for Future | Growwell"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://growwellimf.com/" />
    <meta
      property="og:image"
      content="https://growwellimf.com/images/growwell_logo_01.webp"
    />
    <meta name="author" content="Growwell" />
    <meta name="publisher" content="Growwell" />

    <!--google adsense-->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>

    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/investment-plans.css" />
    <link rel="stylesheet" href="css/retirement-plans.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Header Section (same as other pages) -->
    <header>
      <div class="container">
        <div class="navbar">
          <div class="logo">
            <a href="index.html">
              <img
                src="./images/growwell_logo_01.webp"
                alt="company_logo"
                class="brand-logo"
            /></a>
            <a href="index.html"
              ><h2><span class="highlight">Grow</span>well</h2></a
            >
          </div>

          <div class="hamburger" id="hamburger">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
          </div>

          <nav id="nav-menu">
            <ul id="menu-items">
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Products</a>
                <div class="dropdown-menu">
                  <a href="investment-plans.html">Investment Plans</a>
                  <a href="retirement-plans.html">Retirement Plans</a>
                  <a href="child-plans.html">Child Plans</a>
                  <a href="term-insurance.html">Term Insurance</a>
                  <a href="tax-advisory.html">Tax Advisory</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Renew</a>
                <div class="dropdown-menu">
                  <a href="renew-policy.html">Renew Policy</a>
                  <a href="check-status.html">Check Status</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Claim</a>
                <div class="dropdown-menu">
                  <a href="file-claim.html">File a Claim</a>
                  <a href="track-claim.html">Track Claim</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Help & Support</a>
                <div class="dropdown-menu">
                  <a href="faqs.html">FAQs</a>
                  <a href="contact.html">Contact Us</a>
                  <a href="support.html">Customer Support</a>
                  <a href="advisor.html">Find an Advisor</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">About Us</a>
                <div class="dropdown-menu">
                  <a href="our-story.html">Our Story</a>
                  <a href="leadership.html">Leadership</a>
                  <a href="careers.html">Careers</a>
                </div>
              </li>
            </ul>
          </nav>

          <div class="navbar-actions desktop-only">
            <a href="investment-plans.html" class="btn-primary">Get Started</a>
          </div>
        </div>
      </div>
    </header>

    <!-- Hero Banner for Retirement Plans -->
    <section class="retirement-hero">
      <div class="decorative-circle circle-1"></div>
      <div class="decorative-circle circle-2"></div>
      <div class="decorative-circle circle-3"></div>

      <div class="container">
        <div class="hero-wrapper">
          <div class="hero-left">
            <span class="hero-badge"
              >Retirement Planning <i class="fas fa-check-circle"></i
            ></span>
            <h1>
              Secure Your
              <span class="gradient-text">Financial Future</span> with Our
              Retirement Plans
            </h1>
            <p>
              At Growwell, we understand that retirement planning is a journey,
              not a destination. Whether you're just beginning your career or
              you're approaching the golden years, our retirement solutions are
              crafted to help you build and preserve wealth over time, with the
              right balance of risk management.
            </p>

            <div class="hero-stats">
              <div class="hero-stat-item">
                <span class="stat-value">15K<sup>+</sup></span>
                <span class="stat-label">Happy Retirees</span>
              </div>
              <div class="hero-stat-item">
                <span class="stat-value">22<sup>%</sup></span>
                <span class="stat-label">Avg. Returns</span>
              </div>
              <div class="hero-stat-item">
                <span class="stat-value">₹850<sup>Cr</sup></span>
                <span class="stat-label">Assets Managed</span>
              </div>
            </div>

            <div class="retirement-hero-cta">
              <a href="advisor.html" class="btn-primary btn-hero">
                Talk to an Advisor <i class="fas fa-user-tie"></i
              ></a>
              <a href="#plans" class="btn-secondary btn-hero">
                Explore Plans <i class="fas fa-arrow-right"></i
              ></a>
            </div>
          </div>

          <div class="hero-right">
            <div class="retirement-showcase">
              <!-- Floating Cards -->
              <div class="floating-card floating-card-1">
                <i class="fas fa-shield-alt"></i>
                <p>100% Secure</p>
              </div>
              <div class="floating-card floating-card-2">
                <i class="fas fa-chart-line"></i>
                <p>Growing Returns</p>
              </div>
              <div class="floating-card floating-card-3">
                <i class="fas fa-hand-holding-usd"></i>
                <p>Tax Benefits</p>
              </div>

              <!-- <div class="retirement-illustration">
                <div class="illustration-icon main-icon">
                  <i class="fas fa-umbrella-beach"></i>
                </div>
                <div class="illustration-icon icon-1">
                  <i class="fas fa-home"></i>
                </div>
                <div class="illustration-icon icon-2">
                  <i class="fas fa-plane"></i>
                </div>
                <div class="illustration-icon icon-3">
                  <i class="fas fa-heartbeat"></i>
                </div>
                <div class="pulse-circle"></div>
              </div>

              <div class="sebi-badge">
                <i class="fas fa-award"></i>
                <span>SEBI Registered</span>
              </div> -->
              <div class="right-image">
                <img
                  src="./images/retirement.webp"
                  class="rt-image"
                  alt="right-image"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Retirement Planning Benefits -->
    <section class="investment-benefits retirement-benefits">
      <div class="container">
        <div class="section-header centered">
          <span class="section-subtitle">Why Plan with Us</span>
          <h2>
            <span class="retirement-txt">Retirement</span> Planning Benefits
          </h2>
          <div class="title-underline"></div>
          <p class="section-description">
            Secure your future with our comprehensive retirement planning
            solutions
          </p>
        </div>

        <div class="benefits-grid">
          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-piggy-bank"></i>
            </div>
            <h3 class="benefit-title">Tax Advantages</h3>
            <p class="benefit-description">
              Take advantage of tax-deferred growth and potential tax deductions
              to maximize your retirement savings and reduce your current tax
              burden.
            </p>
            <div class="benefit-link">
              <a href="#plans">Learn More <i class="fas fa-arrow-right"></i></a>
            </div>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3 class="benefit-title">Wealth Accumulation</h3>
            <p class="benefit-description">
              Our retirement plans are designed to help you grow your wealth
              consistently over time, leveraging the power of compounding
              returns.
            </p>
            <div class="benefit-link">
              <a href="#plans">Learn More <i class="fas fa-arrow-right"></i></a>
            </div>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <h3 class="benefit-title">Financial Security</h3>
            <p class="benefit-description">
              Secure your future with guaranteed income options that provide a
              reliable financial foundation throughout your retirement years.
            </p>
            <div class="benefit-link">
              <a href="#plans">Learn More <i class="fas fa-arrow-right"></i></a>
            </div>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-hand-holding-heart"></i>
            </div>
            <h3 class="benefit-title">Legacy Planning</h3>
            <p class="benefit-description">
              Our plans include options for wealth transfer and estate planning
              to ensure your assets go to your loved ones according to your
              wishes.
            </p>
            <div class="benefit-link">
              <a href="#plans">Learn More <i class="fas fa-arrow-right"></i></a>
            </div>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-heartbeat"></i>
            </div>
            <h3 class="benefit-title">Health Coverage</h3>
            <p class="benefit-description">
              Some of our retirement plans include health insurance components
              to protect you against rising medical expenses in your senior
              years.
            </p>
            <div class="benefit-link">
              <a href="#plans">Learn More <i class="fas fa-arrow-right"></i></a>
            </div>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-user-cog"></i>
            </div>
            <h3 class="benefit-title">Personalized Planning</h3>
            <p class="benefit-description">
              Work with our retirement specialists to create a customized plan
              tailored to your unique financial situation and retirement goals.
            </p>
            <div class="benefit-link">
              <a href="#plans">Learn More <i class="fas fa-arrow-right"></i></a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Retirement Plans Section -->
    <section id="plans" class="investment-plans retirement-plans">
      <div class="container">
        <div class="section-header centered">
          <span class="section-subtitle">Our Offerings</span>
          <h2>Retirement <span class="retirement-txt">Solutions </span></h2>
          <div class="title-underline"></div>
          <p class="section-description">
            Choose the retirement plan that aligns with your financial goals and
            timeline
          </p>
        </div>

        <div class="plans-tabs">
          <div class="plan-tab active">All Plans</div>
          <div class="plan-tab">Annuity Plans</div>
          <div class="plan-tab">Pension Plans</div>
          <div class="plan-tab">NPS</div>
        </div>

        <div class="plans-grid">
          <div class="plan-card">
            <div class="plan-badge">Conservative</div>
            <div class="plan-header">
              <h3 class="plan-name">Guaranteed Income Plan</h3>
              <p class="plan-description">
                A low-risk plan that provides guaranteed regular income
                throughout your retirement years.
              </p>
              <div class="plan-price">7-8<sup>%</sup></div>
              <div class="plan-duration">Expected Annual Returns</div>
            </div>
            <ul class="plan-features">
              <li>
                <i class="fas fa-check"></i>
                <span>Guaranteed income stream</span>
              </li>
              <li>
                <i class="fas fa-check"></i>
                <span>Inflation-protected options</span>
              </li>
              <li>
                <i class="fas fa-check"></i>
                <span>Capital preservation focus</span>
              </li>
              <li>
                <i class="fas fa-check"></i>
                <span>Minimum investment: ₹2,00,000</span>
              </li>
              <li>
                <i class="fas fa-check"></i> <span>5-year lock-in period</span>
              </li>
            </ul>
            <div class="plan-cta">
              <a href="contact.html" class="btn-plan"
                >Get Started <i class="fas fa-arrow-right"></i
              ></a>
            </div>
          </div>

          <div class="plan-card popular">
            <div class="plan-badge">Most Popular</div>
            <div class="recommend-tag">
              <span>Recommended</span>
            </div>
            <div class="plan-header">
              <h3 class="plan-name">Balanced Retirement Plan</h3>
              <p class="plan-description">
                A well-balanced approach combining growth potential with
                reliable income options.
              </p>
              <div class="plan-price">9-12<sup>%</sup></div>
              <div class="plan-duration">Expected Annual Returns</div>
            </div>
            <ul class="plan-features">
              <li>
                <i class="fas fa-check"></i> <span>50/50 equity-debt mix</span>
              </li>
              <li>
                <i class="fas fa-check"></i> <span>Tax-advantaged growth</span>
              </li>
              <li>
                <i class="fas fa-check"></i>
                <span>Flexible income options</span>
              </li>
              <li>
                <i class="fas fa-check"></i>
                <span>Minimum investment: ₹5,00,000</span>
              </li>
              <li>
                <i class="fas fa-check"></i> <span>Until age 60 lock-in</span>
              </li>
              <li>
                <i class="fas fa-check"></i> <span>3-year lock-in period</span>
              </li>
            </ul>
            <div class="plan-cta">
              <a href="contact.html" class="btn-plan"
                >Get Started <i class="fas fa-arrow-right"></i
              ></a>
            </div>
          </div>

          <div class="plan-card">
            <div class="plan-badge">Growth Focus</div>
            <div class="plan-header">
              <h3 class="plan-name">National Pension System</h3>
              <p class="plan-description">
                Government-backed pension system offering market-linked returns
                with tax benefits.
              </p>
              <div class="plan-price">10-14<sup>%</sup></div>
              <div class="plan-duration">Expected Annual Returns</div>
            </div>
            <ul class="plan-features">
              <li>
                <i class="fas fa-check"></i> <span>Government regulated</span>
              </li>
              <li>
                <i class="fas fa-check"></i> <span>Enhanced tax benefits</span>
              </li>
              <li>
                <i class="fas fa-check"></i>
                <span>Customizable asset allocation</span>
              </li>
              <li>
                <i class="fas fa-check"></i>
                <span>Minimum contribution: ₹1,000/month</span>
              </li>
              <li>
                <i class="fas fa-check"></i> <span>5-year lock-in period</span>
              </li>
              <li>
                <i class="fas fa-check"></i> <span>Until age 60 lock-in</span>
              </li>
            </ul>
            <div class="plan-cta">
              <a href="contact.html" class="btn-plan"
                >Get Started <i class="fas fa-arrow-right"></i
              ></a>
            </div>
          </div>
        </div>

        <div class="plans-note">
          <div class="note-icon">
            <i class="fas fa-info-circle"></i>
          </div>
          <p>
            All our retirement plans are designed to provide long-term financial
            security and can be customized to meet your specific needs.
            <a href="#contact">Contact our advisors</a> for personalized
            guidance.
          </p>
        </div>
      </div>
    </section>

    <!-- Retirement Calculator -->
    <section
      id="calculator"
      class="investment-calculator retirement-calculator"
    >
      <div class="container">
        <div class="section-header centered">
          <span class="section-subtitle">Plan Your Retirement</span>
          <h2>Retirement <span class="retirement-txt">Calculator</span></h2>
          <div class="title-underline"></div>
          <p class="section-description">
            Estimate how much you need to save for your retirement goals
          </p>
        </div>

        <div class="calculator-container">
          <div class="calculator-form">
            <h3 class="form-title">Input Your Details</h3>

            <div class="calculator-group">
              <label for="current-age" class="calculator-label"
                >Current Age</label
              >
              <input
                type="number"
                id="current-age"
                class="calculator-input"
                placeholder="e.g. 35"
                min="18"
                max="70"
                value="35"
              />
            </div>

            <div class="calculator-group">
              <label for="retirement-age" class="calculator-label"
                >Retirement Age</label
              >
              <div class="slider-container">
                <input
                  type="range"
                  id="retirement-age"
                  class="calculator-slider"
                  min="45"
                  max="75"
                  value="60"
                />
                <div id="retirement-age-value" class="slider-value">
                  60 years
                </div>
              </div>
            </div>

            <div class="calculator-group">
              <label for="monthly-income" class="calculator-label"
                >Current Monthly Income (₹)</label
              >
              <input
                type="number"
                id="monthly-income"
                class="calculator-input"
                placeholder="e.g. 80,000"
                min="10000"
                value="80000"
              />
            </div>

            <div class="calculator-group">
              <label for="current-savings" class="calculator-label"
                >Current Retirement Savings (₹)</label
              >
              <input
                type="number"
                id="current-savings"
                class="calculator-input"
                placeholder="e.g. 10,00,000"
                min="0"
                value="1000000"
              />
            </div>

            <div class="calculator-group">
              <label for="monthly-contribution" class="calculator-label"
                >Monthly Contribution (₹)</label
              >
              <input
                type="number"
                id="monthly-contribution"
                class="calculator-input"
                placeholder="e.g. 15,000"
                min="1000"
                value="15000"
              />
            </div>

            <div class="calculator-group">
              <label for="return-rate" class="calculator-label"
                >Expected Annual Return (%)</label
              >
              <div class="slider-container">
                <input
                  type="range"
                  id="return-rate"
                  class="calculator-slider"
                  min="5"
                  max="15"
                  value="8"
                />
                <div id="return-rate-value" class="slider-value">8%</div>
              </div>
            </div>

            <button class="calculate-btn">
              <i class="fas fa-calculator"></i> Calculate
            </button>
          </div>

          <div class="calculator-result">
            <h3 class="result-title">Your Retirement Projection</h3>

            <div class="result-chart">
              <div class="chart-placeholder">
                <i class="fas fa-chart-pie"></i>
                <p>Interactive chart will appear here</p>
              </div>
            </div>

            <div class="result-stats">
              <div class="result-stat">
                <div class="stat-value">₹1,83,45,625</div>
                <div class="stat-label">Retirement Corpus</div>
              </div>

              <div class="result-stat">
                <div class="stat-value">₹91,728</div>
                <div class="stat-label">Monthly Income</div>
              </div>

              <div class="result-stat">
                <div class="stat-value">25</div>
                <div class="stat-label">Years to Retirement</div>
              </div>

              <div class="result-stat">
                <div class="stat-value">₹15,000</div>
                <div class="stat-label">Required Monthly Saving</div>
              </div>
            </div>

            <div class="result-note">
              <p>
                <i class="fas fa-info-circle"></i> These projections are
                estimates based on your inputs and market assumptions. Actual
                results may vary.
              </p>
            </div>
          </div>
        </div>

        <div class="calculator-benefits">
          <div class="benefit-item">
            <div class="benefit-icon">
              <i class="fas fa-bolt"></i>
            </div>
            <h4>Instant Calculation</h4>
            <p>Get real-time estimates based on your inputs</p>
          </div>

          <div class="benefit-item">
            <div class="benefit-icon">
              <i class="fas fa-lock"></i>
            </div>
            <h4>Private & Secure</h4>
            <p>Your data remains confidential and is never stored</p>
          </div>

          <div class="benefit-item">
            <div class="benefit-icon">
              <i class="fas fa-sliders-h"></i>
            </div>
            <h4>Customizable</h4>
            <p>Adjust parameters to match your exact needs</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Retirement Planning Process -->
    <section class="investment-process">
      <div class="container">
        <div class="section-header centered">
          <span class="section-subtitle">How To Get Started</span>
          <h2>
            <span class="retirement-txt">Retirement </span>Planning Process
          </h2>
          <div class="title-underline"></div>
          <p>
            Our streamlined process helps you create the perfect retirement
            strategy
          </p>
        </div>

        <div class="process-steps">
          <div class="process-step">
            <div class="process-step-number">1</div>
            <div class="process-step-content">
              <div class="process-step-icon">
                <i class="fas fa-bullseye"></i>
              </div>
              <h3 class="process-step-title">Retirement Goals Assessment</h3>
              <p class="process-step-description">
                Meet with our retirement specialists to discuss your lifestyle
                goals, desired retirement age, estimated expenses, and vision
                for your golden years.
              </p>
            </div>
          </div>

          <div class="process-step">
            <div class="process-step-number">2</div>
            <div class="process-step-content">
              <div class="process-step-icon">
                <i class="fas fa-search-dollar"></i>
              </div>
              <h3 class="process-step-title">Financial Analysis</h3>
              <p class="process-step-description">
                Our advisors will analyze your current financial situation,
                including savings, investments, income sources, and future
                expectations to create a comprehensive snapshot.
              </p>
            </div>
          </div>

          <div class="process-step">
            <div class="process-step-number">3</div>
            <div class="process-step-content">
              <div class="process-step-icon">
                <i class="fas fa-route"></i>
              </div>
              <h3 class="process-step-title">Strategy Development</h3>
              <p class="process-step-description">
                Based on your goals and financial analysis, we'll develop a
                personalized retirement strategy that includes appropriate
                investment vehicles, savings rates, and asset allocation.
              </p>
            </div>
          </div>

          <div class="process-step">
            <div class="process-step-number">4</div>
            <div class="process-step-content">
              <div class="process-step-icon">
                <i class="fas fa-tasks"></i>
              </div>
              <h3 class="process-step-title">Plan Implementation</h3>
              <p class="process-step-description">
                Once you approve your retirement plan, we'll handle all the
                documentation and setup for your selected retirement solutions,
                making the process seamless and hassle-free.
              </p>
            </div>
          </div>

          <div class="process-step">
            <div class="process-step-number">5</div>
            <div class="process-step-content">
              <div class="process-step-icon">
                <i class="fas fa-sync-alt"></i>
              </div>
              <h3 class="process-step-title">Regular Review & Optimization</h3>
              <p class="process-step-description">
                Your retirement plan is not set-and-forget. We conduct regular
                reviews to ensure your strategy remains aligned with your goals
                and make adjustments as needed for optimal performance.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="investment-faq">
      <div class="container">
        <div class="section-header centered">
          <span class="section-subtitle">Common Questions</span>
          <h2>Retirement Planning <span class="retirement-txt"> FAQs</span></h2>
          <div class="title-underline"></div>
          <p>Everything you need to know about our retirement plans</p>
        </div>

        <div class="faq-container">
          <div class="faq-item active">
            <div class="faq-question">
              <span
                >What is the ideal time to start planning for retirement?</span
              >
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="faq-answer">
              <div class="faq-answer-content">
                <p>
                  It’s never too early to start planning! The earlier you begin,
                  the more time your investments have to grow. Starting in your
                  20s or 30s allows for maximum compounding benefits, but even
                  if you’re closer to retirement age, it’s still worthwhile to
                  plan. Our advisors can help you determine the best approach
                  based on your current situation and goals.
                </p>
              </div>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <span
                >How much should I contribute to my retirement plan each
                month?</span
              >
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="faq-answer">
              <div class="faq-answer-content">
                <p>
                  The amount you should contribute depends on factors like your
                  retirement goals, desired lifestyle, and age. Our expert
                  advisors will work with you to create a customized strategy
                  that fits your financial situation. As a general guideline, we
                  recommend contributing at least 15% of your income, but the
                  exact figure can vary based on your needs
                </p>
              </div>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <span
                >What types of investment options are included in the retirement
                plans?</span
              >
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="faq-answer">
              <div class="faq-answer-content">
                <p>
                  Our retirement plans offer a variety of investment options,
                  including equities, fixed-income securities, and alternative
                  investments. The right mix of assets will depend on your risk
                  tolerance and investment horizon. We work with you to build a
                  diversified portfolio that ensures growth while managing risk.
                </p>
              </div>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <span
                >Can I change my retirement plan as my circumstances
                change??</span
              >
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="faq-answer">
              <div class="faq-answer-content">
                <p>
                  Yes! Our retirement plans are flexible. Whether your financial
                  situation changes or you reach a different stage in your
                  career, we allow for adjustments to your investment strategy.
                  Our advisors are always available to review and refine your
                  plan as needed.
                </p>
              </div>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <span>How do I track the performance of my retirement plan?</span>
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="faq-answer">
              <div class="faq-answer-content">
                <p>
                  You can easily track the performance of your retirement plan
                  through our user-friendly mobile app and online portal. Our
                  dashboard provides real-time updates on your investments,
                  allowing you to see how your wealth is growing and make
                  adjustments when necessary.
                </p>
              </div>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <span>Should I choose NPS or a pension plan?</span>
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="faq-answer">
              <div class="faq-answer-content">
                <p>
                  The choice between NPS (National Pension System) and
                  traditional pension plans depends on your specific needs. NPS
                  offers potentially higher returns through market-linked
                  investments, greater flexibility in asset allocation, and
                  additional tax benefits. Traditional pension plans provide
                  more certainty with guaranteed returns, often include
                  insurance benefits, and may be simpler to manage. Many
                  retirement strategies incorporate both types of products to
                  balance growth potential with guaranteed income. Our advisors
                  can help determine the optimal mix based on your risk
                  tolerance and retirement goals.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
      <div class="container">
          <div class="cta-content">
              <span class="section-subtitle">Start Planning Today</span>
              <h2>Ready to Secure Your Retirement?</h2>
              <p>Schedule a free consultation with our retirement planning advisors to get started.</p>
              <div class="cta-buttons">
                  <a href="schedule_consultation.html" class="btn btn-primary"><i class="fas fa-calendar-alt"></i> Schedule Consultation</a>
                  <a href="tel:+15551234567" class="btn btn-secondary"><i class="fas fa-phone-alt"></i> Call Us Now</a>
              </div>
              <div class="trust-indicators">
                  <div class="trust-item">
                      <i class="fas fa-check-circle"></i>
                      <span>Free Consultation</span>
                  </div>
                  <div class="trust-item">
                      <i class="fas fa-check-circle"></i>
                      <span>Expert Advisors</span>
                  </div>
                  <div class="trust-item">
                      <i class="fas fa-check-circle"></i>
                      <span>Personalized Plans</span>
                  </div>
              </div>
          </div>
      </div>
  </section>

    <!-- Footer (same as other pages) -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <div class="footer-logo-img">
              <img
                src="./images/growwell_logo_01.webp"
                alt="Growwell Logo"
                loading="lazy"
                class="footer-logo-image"
              />
            </div>
            <p>Your partner for financial growth and security</p>
            <div class="social-icons">
              <a
                href="https://www.facebook.com/growwellimf/"
                target="/"
                aria-label="Follow us on Facebook"
                ><i class="fab fa-facebook-f"></i
              ></a>
              <a
                href="https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08"
                target="/"
                aria-label="Follow us on X"
                ><i class="fab fa-twitter"></i
              ></a>
              <a
                href="https://www.linkedin.com/company/grow-well-imf/about/"
                target="/"
                aria-label="Follow us on Linkedin"
                ><i class="fab fa-linkedin-in"></i
              ></a>
              <a
                href="https://www.instagram.com/growwell.imf/"
                target="/"
                aria-label="Follow us on Instagram"
                ><i class="fab fa-instagram"></i
              ></a>
            </div>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h3>Quick Links</h3>
              <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="./investment-plans.html">Products</a></li>
                <li><a href="./renew-policy.html">Renew</a></li>
                <li><a href="./advisor.html">Testimonials</a></li>
                <li><a href="./contact.html">Contact</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Services</h3>
              <ul>
                <li><a href="investment-plans.html">Financial Planning</a></li>
                <li><a href="child-plans.html">Child Planning</a></li>
                <li><a href="investment-plans.html">Investment Planning</a></li>
                <li><a href="retirement-plans.html">Retirement Planning</a></li>
                <li><a href="term-insurance.html">Term Insurance</a></li>
                <li><a href="#">Tax Advisory</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Resources</h3>
              <ul>
                <li><a href="leadership.html">Leadership</a></li>
                <li><a href="advisor.html">Advisor</a></li>
                <li>
                  <a href="retirement-plans.html">Financial Calculators</a>
                </li>
                <li><a href="faqs.html">FAQs</a></li>
                <li><a href="contact.html">contact</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2023 Growwell Financial Advisors. All rights reserved.</p>
          <ul class="footer-legal">
            <li><a href="privacy-policy.html">Privacy Policy</a></li>
            <li><a href="terms-of-service.html">Terms of Service</a></li>
            <li><a href="cookie-policy.html">Cookie Policy</a></li>
          </ul>
        </div>
      </div>
    </footer>

    <script src="js/script.js"></script>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Growwell",
        "url": "https://growwellimf.com",
        "logo": "https://growwellimf.com/images/growwell_logo_01.webp",
        "sameAs": [
          "https://www.facebook.com/growwellimf/",
          "https://www.instagram.com/growwell.imf/",
          "https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08",
          "https://www.linkedin.com/company/grow-well-imf/about/"
        ]
      }
    </script>
    <script>
      // Interactive elements for Retirement page
      document.addEventListener("DOMContentLoaded", function () {
        // Retirement age slider
        const retirementAgeSlider = document.getElementById("retirement-age");
        const retirementAgeValue = document.getElementById(
          "retirement-age-value"
        );

        if (retirementAgeSlider && retirementAgeValue) {
          retirementAgeSlider.addEventListener("input", function () {
            retirementAgeValue.textContent = this.value + " years";
          });
        }

        // Return rate slider
        const returnRateSlider = document.getElementById("return-rate");
        const returnRateValue = document.getElementById("return-rate-value");

        if (returnRateSlider && returnRateValue) {
          returnRateSlider.addEventListener("input", function () {
            returnRateValue.textContent = this.value + "%";
          });
        }

        // Plan tabs functionality
        const planTabs = document.querySelectorAll(".plan-tab");

        planTabs.forEach((tab) => {
          tab.addEventListener("click", () => {
            // Remove active class from all tabs
            planTabs.forEach((t) => t.classList.remove("active"));

            // Add active class to clicked tab
            tab.classList.add("active");

            // Here you would typically filter the plans
            // For demonstration, we're just updating the UI
          });
        });

        // Calculate button functionality
        const calculateBtn = document.querySelector(".calculate-btn");

        if (calculateBtn) {
          calculateBtn.addEventListener("click", function () {
            // Add animation effect
            const resultStats = document.querySelectorAll(".result-stat");

            resultStats.forEach((stat, index) => {
              stat.style.opacity = "0";

              setTimeout(() => {
                stat.style.transition = "opacity 0.5s, transform 0.5s";
                stat.style.opacity = "1";
                stat.style.transform = "translateY(0)";
              }, 300 + index * 150);
            });

            // Here you would calculate actual values based on inputs
            // For demonstration, we're just showing animation
          });
        }

        // FAQ accordion functionality

        document.addEventListener("DOMContentLoaded", function () {
          const faqItems = document.querySelectorAll(".faq-item");

          faqItems.forEach((item) => {
            const question = item.querySelector(".faq-question");

            question.addEventListener("click", () => {
              // Collapse all other answers
              faqItems.forEach((otherItem) => {
                if (otherItem !== item) {
                  otherItem.classList.remove("active");
                }
              });

              // Toggle current item
              item.classList.toggle("active");
            });
          });
        });

        // Add hover effects to benefit cards
        const benefitCards = document.querySelectorAll(".benefit-card");

        benefitCards.forEach((card) => {
          card.addEventListener("mouseenter", function () {
            const icon = this.querySelector(".benefit-icon");

            if (icon) {
              icon.style.transform = "scale(1.1) rotate(5deg)";
            }
          });

          card.addEventListener("mouseleave", function () {
            const icon = this.querySelector(".benefit-icon");

            if (icon) {
              icon.style.transform = "";
            }
          });
        });

        // Animate the process steps on scroll
        const observerOptions = {
          threshold: 0.2,
          rootMargin: "0px 0px -100px 0px",
        };

        const processObserver = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              entry.target.classList.add("animated");
              processObserver.unobserve(entry.target);
            }
          });
        }, observerOptions);

        const processSteps = document.querySelectorAll(".process-step");

        processSteps.forEach((step) => {
          processObserver.observe(step);
        });
      });

      document.addEventListener("DOMContentLoaded", function () {
        // Get elements
        const currentAge = document.getElementById("current-age");
        const retirementAge = document.getElementById("retirement-age");
        const monthlyIncome = document.getElementById("monthly-income");
        const currentSavings = document.getElementById("current-savings");
        const monthlyContribution = document.getElementById(
          "monthly-contribution"
        );
        const returnRate = document.getElementById("return-rate");

        const retirementAgeValue = document.getElementById(
          "retirement-age-value"
        );
        const returnRateValue = document.getElementById("return-rate-value");

        const resultStats = document.querySelectorAll(
          ".result-stat .stat-value"
        );
        const calculateBtn = document.querySelector(".calculate-btn");

        function updateSliders() {
          retirementAgeValue.textContent = retirementAge.value + " years";
          returnRateValue.textContent = returnRate.value + "%";
        }

        function calculateRetirement() {
          const currentAgeVal = parseInt(currentAge.value) || 0;
          const retirementAgeVal = parseInt(retirementAge.value) || 0;
          const monthlyIncomeVal = parseFloat(monthlyIncome.value) || 0;
          const currentSavingsVal = parseFloat(currentSavings.value) || 0;
          const monthlyContributionVal =
            parseFloat(monthlyContribution.value) || 0;
          const returnRateVal = (parseFloat(returnRate.value) || 0) / 100;

          const yearsToRetirement = retirementAgeVal - currentAgeVal;
          const monthsToRetirement = yearsToRetirement * 12;
          const monthlyReturnRate = returnRateVal / 12;

          // Future Value of current savings
          const futureValueCurrentSavings =
            currentSavingsVal *
            Math.pow(1 + monthlyReturnRate, monthsToRetirement);

          // Future Value of future contributions
          const futureValueContributions =
            monthlyContributionVal *
            ((Math.pow(1 + monthlyReturnRate, monthsToRetirement) - 1) /
              monthlyReturnRate) *
            (1 + monthlyReturnRate);

          // Total Retirement Corpus
          const totalRetirementCorpus =
            futureValueCurrentSavings + futureValueContributions;

          // Estimate monthly income after retirement
          // Here we assume 4% safe withdrawal rule (standard in retirement planning)
          const estimatedMonthlyIncome = (totalRetirementCorpus * 0.04) / 12;

          // Format currency nicely
          const formatCurrency = (num) => {
            return "₹" + num.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
          };

          // Update results
          if (resultStats.length >= 4) {
            resultStats[0].textContent = formatCurrency(totalRetirementCorpus); // Retirement Corpus
            resultStats[1].textContent = formatCurrency(estimatedMonthlyIncome); // Monthly Income
            resultStats[2].textContent =
              yearsToRetirement > 0 ? yearsToRetirement : 0; // Years to Retirement
            resultStats[3].textContent = formatCurrency(monthlyContributionVal); // Monthly Contribution
          }
        }

        function updateCalculator() {
          updateSliders();
        }

        // Event listeners
        retirementAge.addEventListener("input", updateCalculator);
        returnRate.addEventListener("input", updateCalculator);

        calculateBtn.addEventListener("click", function (e) {
          e.preventDefault(); // Prevent form submission
          calculateRetirement();
        });

        // Initial load
        updateCalculator();
      });
    </script>
  </body>
</html>
