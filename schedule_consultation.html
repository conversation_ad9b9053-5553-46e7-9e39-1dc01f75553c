<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Schedule Free Consultation | Growwell Financial Advisors</title>
    <meta name="description" content="Schedule a free consultation with our expert financial advisors. Get personalized financial planning and investment advice tailored to your goals." />
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
</head>
<body>
    <!-- Header Section -->
    <header>
        <div class="container">
            <div class="navbar">
                <div class="logo">
                    <a href="index.html">
                        <img src="./images/growwell_logo_01.webp" alt="company_logo" class="brand-logo" />
                    </a>
                    <a href="index.html"><h2><span class="highlight">Grow</span>well</h2></a>
                </div>

                <div class="hamburger" id="hamburger">
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                </div>

                <nav id="nav-menu">
                    <ul id="menu-items">
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle">Products</a>
                            <div class="dropdown-menu">
                                <a href="investment-plans.html">Investment Plans</a>
                                <a href="retirement-plans.html">Retirement Plans</a>
                                <a href="child-plans.html">Child Plans</a>
                                <a href="term-insurance.html">Term Insurance</a>
                            </div>
                        </li>
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle">Renew</a>
                            <div class="dropdown-menu">
                                <a href="renew-policy.html">Renew Policy</a>
                                <a href="check-status.html">Check Status</a>
                            </div>
                        </li>
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle">Claim</a>
                            <div class="dropdown-menu">
                                <a href="file-claim.html">File a Claim</a>
                                <a href="track-claim.html">Track Claim</a>
                            </div>
                        </li>
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle">Help & Support</a>
                            <div class="dropdown-menu">
                                <a href="faqs.html">FAQs</a>
                                <a href="contact.html">Contact Us</a>
                                <a href="support.html">Customer Support</a>
                                <a href="advisor.html">Find an Advisor</a>
                            </div>
                        </li>
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle">About Us</a>
                            <div class="dropdown-menu">
                                <a href="our-story.html">Our Story</a>
                                <a href="leadership.html">Leadership</a>
                                <a href="careers.html">Careers</a>
                            </div>
                        </li>
                    </ul>
                </nav>

                <div class="navbar-actions desktop-only">
                    <a href="investment-plans.html" class="btn-primary">Get Started</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" style="padding-top: 120px;">
        <div class="container">
            <div class="section-header">
                <span class="section-subtitle">Free Consultation</span>
                <h2>Schedule Your <span class="client-txt">Financial</span> Consultation</h2>
                <p>Get personalized financial advice from our certified experts. Book your free consultation today and take the first step towards financial freedom.</p>
            </div>
        </div>
    </section>

    <!-- Consultation Form Section -->
    <section class="contact" style="padding-top: 40px;">
        <div class="container">
            <div class="contact-content">
                <div class="contact-left">
                    <div class="contact-info">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="info-content">
                                <h3>Free Consultation</h3>
                                <p>Schedule a complimentary 30-minute session with our certified financial advisors</p>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-user-tie"></i>
                            </div>
                            <div class="info-content">
                                <h3>Expert Advisors</h3>
                                <p>SEBI registered investment advisors with 10+ years of experience</p>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="info-content">
                                <h3>Personalized Plans</h3>
                                <p>Customized financial strategies tailored to your unique goals and risk profile</p>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="info-content">
                                <h3>Secure & Confidential</h3>
                                <p>Your financial information is protected with bank-level security</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="contact-right">
                    <div class="contact-form">
                        <h3>Book Your Free Consultation</h3>
                        <form id="consultation-form">
                            <div class="form-group">
                                <label for="name">Full Name *</label>
                                <input type="text" id="name" name="name" class="form-control" required>
                            </div>

                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" class="form-control" required>
                            </div>

                            <div class="form-group">
                                <label for="phone">Phone Number *</label>
                                <input type="tel" id="phone" name="phone" class="form-control" required>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="preferred-date">Preferred Date *</label>
                                    <input type="date" id="preferred-date" name="preferred-date" class="form-control" required>
                                </div>

                                <div class="form-group">
                                    <label for="preferred-time">Preferred Time *</label>
                                    <select id="preferred-time" name="preferred-time" class="form-control" required>
                                        <option value="">Select a time</option>
                                        <option value="9:00 AM">9:00 AM</option>
                                        <option value="10:00 AM">10:00 AM</option>
                                        <option value="11:00 AM">11:00 AM</option>
                                        <option value="1:00 PM">1:00 PM</option>
                                        <option value="2:00 PM">2:00 PM</option>
                                        <option value="3:00 PM">3:00 PM</option>
                                        <option value="4:00 PM">4:00 PM</option>
                                        <option value="5:00 PM">5:00 PM</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="consultation-type">Consultation Type</label>
                                <select id="consultation-type" name="consultation-type" class="form-control">
                                    <option value="">Select consultation type</option>
                                    <option value="investment-planning">Investment Planning</option>
                                    <option value="retirement-planning">Retirement Planning</option>
                                    <option value="tax-planning">Tax Planning</option>
                                    <option value="insurance-planning">Insurance Planning</option>
                                    <option value="education-planning">Education Planning</option>
                                    <option value="general-financial-planning">General Financial Planning</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="message">Tell us about your financial goals</label>
                                <textarea id="message" name="message" class="form-control" rows="4" placeholder="Share your investment goals, current financial situation, or any specific questions you have..."></textarea>
                            </div>

                            <button type="submit" class="btn-primary">
                                <i class="fas fa-calendar-check"></i> Schedule Consultation
                            </button>
                        </form>

                        <div id="form-message" class="form-message"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Trust Indicators Section -->
    <section class="cta" style="padding: 60px 0;">
        <div class="container">
            <div class="trust-indicators">
                <div class="trust-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Free Consultation</span>
                </div>
                <div class="trust-item">
                    <i class="fas fa-check-circle"></i>
                    <span>SEBI Registered</span>
                </div>
                <div class="trust-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Expert Advisors</span>
                </div>
                <div class="trust-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Personalized Plans</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer Section -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <div class="footer-logo-img">
                        <img src="./images/growwell_logo_01.webp" alt="Growwell Logo" loading="lazy" class="footer-logo-image" />
                    </div>
                    <p>Your partner for financial growth and security</p>
                    <div class="social-icons">
                        <a href="https://www.facebook.com/growwellimf/" target="/" aria-label="Follow us on Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08" target="/" aria-label="Follow us on X"><i class="fab fa-twitter"></i></a>
                        <a href="https://www.linkedin.com/company/grow-well-imf/about/" target="/" aria-label="Follow us on Linkedin"><i class="fab fa-linkedin-in"></i></a>
                        <a href="https://www.instagram.com/growwell.imf/" target="/" aria-label="Follow us on Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <div class="footer-column">
                        <h3>Quick Links</h3>
                        <ul>
                            <li><a href="index.html">Home</a></li>
                            <li><a href="./investment-plans.html">Products</a></li>
                            <li><a href="./renew-policy.html">Renew</a></li>
                            <li><a href="./advisor.html">Testimonials</a></li>
                            <li><a href="./contact.html">Contact</a></li>
                        </ul>
                    </div>
                    <div class="footer-column">
                        <h3>Services</h3>
                        <ul>
                            <li><a href="investment-plans.html">Financial Planning</a></li>
                            <li><a href="child-plans.html">Child Planning</a></li>
                            <li><a href="investment-plans.html">Investment Planning</a></li>
                            <li><a href="retirement-plans.html">Retirement Planning</a></li>
                            <li><a href="term-insurance.html">Term Insurance</a></li>
                            <li><a href="tax-advisory.html">Tax Advisory</a></li>
                        </ul>
                    </div>
                    <div class="footer-column">
                        <h3>Resources</h3>
                        <ul>
                            <li><a href="leadership.html">Leadership</a></li>
                            <li><a href="advisor.html">Advisor</a></li>
                            <li><a href="retirement-plans.html">Financial Calculators</a></li>
                            <li><a href="faqs.html">FAQs</a></li>
                            <li><a href="contact.html">contact</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 Growwell Financial Advisors. All rights reserved.</p>
                <ul class="footer-legal">
                    <li><a href="privacy-policy.html">Privacy Policy</a></li>
                    <li><a href="terms-of-service.html">Terms of Service</a></li>
                    <li><a href="cookie-policy.html">Cookie Policy</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script>
        document.getElementById('consultation-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const form = e.target;
            const formMessage = document.getElementById('form-message');

            // Basic form validation
            const name = form.name.value.trim();
            const email = form.email.value.trim();
            const phone = form.phone.value.trim();
            const date = form['preferred-date'].value;
            const time = form['preferred-time'].value;

            if (!name || !email || !phone || !date || !time) {
                formMessage.textContent = 'Please fill out all required fields.';
                formMessage.className = 'form-message error';
                formMessage.style.display = 'block';
                return;
            }

            // Simple email validation
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailPattern.test(email)) {
                formMessage.textContent = 'Please enter a valid email address.';
                formMessage.className = 'form-message error';
                formMessage.style.display = 'block';
                return;
            }

            // Check if date is in the future
            const selectedDate = new Date(date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (selectedDate < today) {
                formMessage.textContent = 'Please select a future date for your consultation.';
                formMessage.className = 'form-message error';
                formMessage.style.display = 'block';
                return;
            }

            // Simulate form submission
            formMessage.textContent = 'Thank you for scheduling your consultation! We will contact you within 24 hours to confirm your appointment and provide meeting details.';
            formMessage.className = 'form-message success';
            formMessage.style.display = 'block';

            // Reset form
            form.reset();

            // Scroll to message
            formMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
        });

        // Set minimum date to today
        document.getElementById('preferred-date').min = new Date().toISOString().split('T')[0];
    </script>
</body>
</html>