<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Schedule Consultation</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      background-color: #f4f4f4;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    .cta {
      padding: 60px 0;
      background-color: #fff;
      text-align: center;
    }

    .section-subtitle {
      display: block;
      color: #666;
      font-size: 1.1rem;
      text-transform: uppercase;
      letter-spacing: 2px;
      margin-bottom: 10px;
    }

    .cta-content h2 {
      font-size: 2.5rem;
      color: #333;
      margin-bottom: 20px;
    }

    .cta-content p {
      font-size: 1.1rem;
      color: #666;
      max-width: 600px;
      margin: 0 auto 30px;
    }

    .cta-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-bottom: 40px;
    }

    .btn {
      display: inline-flex;
      align-items: center;
      padding: 12px 24px;
      text-decoration: none;
      font-size: 1rem;
      border-radius: 5px;
      transition: background-color 0.3s;
    }

    .btn-secondary {
      background-color: #6c757d;
      color: #fff;
      border: 1px solid #6c757d;
    }

    .btn-secondary:hover {
      background-color: #545b62;
      border-color: #545b62;
    }

    .btn i {
      margin-right: 8px;
    }

    .trust-indicators {
      display: flex;
      justify-content: center;
      gap: 30px;
      margin-top: 20px;
    }

    .trust-item {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #333;
      font-size: 1rem;
    }

    .trust-item i {
      color: #28a745;
    }

    .consultation-form {
      max-width: 600px;
      margin: 40px auto;
      padding: 20px;
      background-color: #f9f9f9;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
      color: #333;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-size: 1rem;
    }

    .form-group textarea {
      resize: vertical;
      min-height: 100px;
    }

    .submit-btn {
      background-color: #007bff;
      color: #fff;
      border: none;
      padding: 12px 24px;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .submit-btn:hover {
      background-color: #0056b3;
    }

    #form-message {
      margin-top: 20px;
      padding: 10px;
      border-radius: 5px;
      display: none;
    }

    .success {
      background-color: #d4edda;
      color: #155724;
    }

    .error {
      background-color: #f8d7da;
      color: #721c24;
    }
  </style>
</head>
<body>
  <section class="cta">
      <div class="consultation-form">
        <form id="consultation-form">
          <div class="form-group">
            <label for="name">Full Name</label>
            <input type="text" id="name" name="name" required>
          </div>
          <div class="form-group">
            <label for="email">Email Address</label>
            <input type="email" id="email" name="email" required>
          </div>
          <div class="form-group">
            <label for="phone">Phone Number</label>
            <input type="tel" id="phone" name="phone" required>
          </div>
          <div class="form-group">
            <label for="preferred-date">Preferred Date</label>
            <input type="date" id="preferred-date" name="preferred-date" required>
          </div>
          <div class="form-group">
            <label for="preferred-time">Preferred Time</label>
            <select id="preferred-time" name="preferred-time" required>
              <option value="">Select a time</option>
              <option value="9:00 AM">9:00 AM</option>
              <option value="10:00 AM">10:00 AM</option>
              <option value="11:00 AM">11:00 AM</option>
              <option value="1:00 PM">1:00 PM</option>
              <option value="2:00 PM">2:00 PM</option>
              <option value="3:00 PM">3:00 PM</option>
            </select>
          </div>
          <div class="form-group">
            <label for="message">Additional Information</label>
            <textarea id="message" name="message" placeholder="Tell us about your investment goals..."></textarea>
          </div>
          <button type="submit" class="submit-btn">Submit Request</button>
        </form>
        <div id="form-message"></div>
      </div>
    </div>
  </section>

  <script>
    document.getElementById('consultation-form').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const form = e.target;
      const formMessage = document.getElementById('form-message');
      
      // Basic form validation
      const name = form.name.value.trim();
      const email = form.email.value.trim();
      const phone = form.phone.value.trim();
      const date = form['preferred-date'].value;
      const time = form['preferred-time'].value;

      if (!name || !email || !phone || !date || !time) {
        formMessage.textContent = 'Please fill out all required fields.';
        formMessage.className = 'error';
        formMessage.style.display = 'block';
        return;
      }

      // Simple email validation
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailPattern.test(email)) {
        formMessage.textContent = 'Please enter a valid email address.';
        formMessage.className = 'error';
        formMessage.style.display = 'block';
        return;
      }

      // Simulate form submission
      formMessage.textContent = 'Thank you for your request! We will contact you soon to confirm your consultation.';
      formMessage.className = 'success';
      formMessage.style.display = 'block';
      
      // Reset form
      form.reset();
    });
  </script>
</body>
</html>