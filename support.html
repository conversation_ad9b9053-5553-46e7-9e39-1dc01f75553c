<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Customer Support | Financial Help | Growwell</title>
    <meta
      name="description"
      content="Get in touch with Growwell financial experts today. We’re here to answer your questions and help you plan your financial future with confidence."
    />
    <meta
      name="keywords"
      content="growwell, financial experts, financial help, financial future"
    />
    <meta name="robots" content="support, follow" />
    <link rel="canonical" href="https://growwellimf.com/" />
    <meta
      property="og:title"
      content="Customer Support | Financial Help | Growwell"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://growwellimf.com/" />
    <meta
      property="og:image"
      content="https://growwellimf.com/images/growwell_logo_01.webp"
    />
    <meta name="author" content="Growwell" />
    <meta name="publisher" content="Growwell" />

    <!--google adsense-->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>

    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/support.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Header Section -->
    <header>
      <div class="container">
        <div class="navbar">
          <div class="logo">
            <a href="index.html">
              <img
                src="./images/growwell_logo_01.webp"
                alt="company_logo"
                class="brand-logo"
            /></a>
            <a href="index.html"
              ><h2><span class="highlight">Grow</span>well</h2></a
            >
          </div>

          <div class="hamburger" id="hamburger">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
          </div>

          <nav id="nav-menu">
            <ul id="menu-items">
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Products</a>
                <div class="dropdown-menu">
                  <a href="investment-plans.html">Investment Plans</a>
                  <a href="retirement-plans.html">Retirement Plans</a>
                  <a href="child-plans.html">Child Plans</a>
                  <a href="term-insurance.html">Term Insurance</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Renew</a>
                <div class="dropdown-menu">
                  <a href="renew-policy.html">Renew Policy</a>
                  <a href="check-status.html">Check Status</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Claim</a>
                <div class="dropdown-menu">
                  <a href="file-claim.html">File a Claim</a>
                  <a href="track-claim.html">Track Claim</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Help & Support</a>
                <div class="dropdown-menu">
                  <a href="faqs.html">FAQs</a>
                  <a href="contact.html">Contact Us</a>
                  <a href="support.html">Customer Support</a>
                  <a href="advisor.html">Find an Advisor</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">About Us</a>
                <div class="dropdown-menu">
                  <a href="our-story.html">Our Story</a>
                  <a href="leadership.html">Leadership</a>
                  <a href="careers.html">Careers</a>
                </div>
              </li>
            </ul>
          </nav>

          <div class="navbar-actions desktop-only">
            <a href="investment-plans.html" class="btn-primary">Get Started</a>
          </div>
        </div>
      </div>
    </header>

    <!-- Support Hero Section -->
    <section class="support-hero">
      <div class="container">
        <div class="support-hero-content">
          <h1>Customer Support</h1>
          <p>
            We're here to help you with any questions or issues you may have.
          </p>
        </div>
      </div>
    </section>

    <!-- Support Main Section -->
    <section class="support-section">
      <div class="container">
        <div class="support-container">
          <!-- Support Options -->
          <div class="section-header">
            <span class="section-subtitle">How Can We Help?</span>
            <h2>
              Choose Your <span class="support-txt"> Support Option</span>
            </h2>
            <p>Select the support channel that works best for you</p>
          </div>

          <div class="support-options">
            <div class="support-option">
              <div class="support-icon">
                <i class="fas fa-headset"></i>
              </div>
              <h3>Call Us</h3>
              <p>
                Speak directly with our customer support team for immediate
                assistance with your queries.
              </p>
              <p><strong>Toll Free:</strong> 1800 123 456</p>
              <p><strong>Hours:</strong> Mon-Fri, 9AM-6PM</p>
              <a href="tel:+911800123456" class="btn-primary">Call Now</a>
            </div>

            <div class="support-option">
              <div class="support-icon">
                <i class="fas fa-comments"></i>
              </div>
              <h3>Live Chat</h3>
              <p>
                Chat with our support representatives in real-time for quick
                answers to your questions.
              </p>
              <p><strong>Response Time:</strong> Under 2 minutes</p>
              <p><strong>Hours:</strong> 24/7 Support</p>
              <a href="#" class="btn-primary">Start Chat</a>
            </div>

            <div class="support-option">
              <div class="support-icon">
                <i class="fas fa-envelope"></i>
              </div>
              <h3>Email Support</h3>
              <p>
                Send us an email with your query and our team will get back to
                you within 24 hours.
              </p>
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Response:</strong> Within 24 hours</p>
              <a href="mailto:<EMAIL>" class="btn-primary"
                >Email Us</a
              >
            </div>
          </div>

          <!-- Support Ticket Form -->
          <div class="support-ticket">
            <div class="form-success" id="ticketSuccess">
              <p>
                <strong>Thank you for submitting your support ticket!</strong>
                Our team will review your request and respond within 24 hours.
                Your ticket number is
                <strong>#GW<span id="ticketNumber">12345</span></strong
                >.
              </p>
            </div>

            <h2>Submit a Support Ticket</h2>
            <form id="supportForm">
              <div class="form-row">
                <div class="form-group">
                  <label for="name">Full Name</label>
                  <input
                    type="text"
                    id="name"
                    class="form-control"
                    placeholder="Enter your full name"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="email">Email Address</label>
                  <input
                    type="email"
                    id="email"
                    class="form-control"
                    placeholder="Enter your email address"
                    required
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="phone">Phone Number</label>
                  <input
                    type="tel"
                    id="phone"
                    class="form-control"
                    placeholder="Enter your phone number"
                  />
                </div>

                <div class="form-group">
                  <label for="policyNumber"
                    >Policy Number (if applicable)</label
                  >
                  <input
                    type="text"
                    id="policyNumber"
                    class="form-control"
                    placeholder="Enter your policy number"
                  />
                </div>
              </div>

              <div class="form-group">
                <label for="issueType">Issue Type</label>
                <select id="issueType" class="form-control" required>
                  <option value="" selected disabled>Select issue type</option>
                  <option value="Account Access">Account Access</option>
                  <option value="Policy Information">Policy Information</option>
                  <option value="Payment Issue">Payment Issue</option>
                  <option value="Claim Status">Claim Status</option>
                  <option value="Technical Issue">Technical Issue</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div class="form-group">
                <label for="priority">Priority</label>
                <select id="priority" class="form-control" required>
                  <option value="" selected disabled>Select priority</option>
                  <option value="Low">Low - General inquiry</option>
                  <option value="Medium">
                    Medium - Issue affecting service
                  </option>
                  <option value="High">High - Urgent matter</option>
                </select>
              </div>

              <div class="form-group">
                <label for="description">Describe Your Issue</label>
                <textarea
                  id="description"
                  class="form-control"
                  placeholder="Please provide details about your issue"
                  required
                ></textarea>
              </div>

              <div class="form-group">
                <label for="attachment">Attach Files (optional)</label>
                <input type="file" id="attachment" class="form-control" />
                <small
                  >Max file size: 5MB. Accepted formats: PDF, JPG, PNG</small
                >
              </div>

              <button type="submit" class="btn-primary">Submit Ticket</button>
            </form>
          </div>

          <!-- FAQ Preview -->
          <div class="faq-preview">
            <h2>Frequently Asked Questions</h2>

            <div class="faq-items">
              <div class="faq-item">
                <div class="faq-question">How do I check my policy status?</div>
                <div class="faq-answer">
                  <p>
                    You can check your policy status by logging into your
                    account on our website or mobile app. Navigate to the "My
                    Policies" section to view all your active policies and their
                    current status. Alternatively, you can call our customer
                    support at 1800 123 456 or email <NAME_EMAIL>
                    with your policy number.
                  </p>
                </div>
              </div>

              <div class="faq-item">
                <div class="faq-question">
                  How do I update my contact information?
                </div>
                <div class="faq-answer">
                  <p>
                    To update your contact information, log into your account
                    and go to the "Profile" or "Account Settings" section. Here,
                    you can update your phone number, email address, and mailing
                    address. For security reasons, some changes may require
                    additional verification. If you're having trouble, please
                    contact our customer support team for assistance.
                  </p>
                </div>
              </div>

              <div class="faq-item">
                <div class="faq-question">
                  What should I do if I forgot my password?
                </div>
                <div class="faq-answer">
                  <p>
                    If you've forgotten your password, click on the "Forgot
                    Password" link on the login page. Enter your registered
                    email address, and we'll send you a password reset link.
                    Follow the instructions in the email to create a new
                    password. If you don't receive the email within a few
                    minutes, check your spam folder or contact our support team
                    for assistance.
                  </p>
                </div>
              </div>
            </div>

            <div class="faq-more">
              <a href="faqs.html" class="btn-outline">View All FAQs</a>
            </div>
          </div>

          <!-- Support Resources -->
          <div class="support-resources">
            <div class="section-header">
              <span class="section-subtitle">Self-Help Resources</span>
              <h2>
                Helpful Resources & <span class="guide-txt"> Guides</span>
              </h2>
              <p>
                Explore our knowledge base to find answers and learn more about
                our services
              </p>
            </div>

            <div class="resources-grid">
              <div class="resource-card">
                <div class="resource-image">
                  <i class="fas fa-book"></i>
                </div>
                <div class="resource-content">
                  <h3>User Guides</h3>
                  <p>
                    Step-by-step guides to help you navigate our platform and
                    make the most of your financial products.
                  </p>
                  <a href="#" class="btn-outline" onclick="openGuidesModal()">View Guides</a>
                </div>
              </div>

              <div class="resource-card">
                <div class="resource-image">
                  <i class="fas fa-video"></i>
                </div>
                <div class="resource-content">
                  <h3>Video Tutorials</h3>
                  <p>
                    Watch our video tutorials to learn how to use our services,
                    manage your investments, and more.
                  </p>
                  <a href="#" class="btn-outline" onclick="openVideosModal()">Watch Videos</a>
                </div>
              </div>

              <div class="resource-card">
                <div class="resource-image">
                  <i class="fas fa-file-alt"></i>
                </div>
                <div class="resource-content">
                  <h3>Document Center</h3>
                  <p>
                    Access and download important documents, forms, and
                    brochures related to your financial products.
                  </p>
                  <a href="#" class="btn-outline" onclick="openDocumentsModal()">Access Documents</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Animated Shapes -->
      <div class="shape-animation support-shape-1"></div>
      <div class="shape-animation support-shape-2"></div>
    </section>

    <!-- Footer Section -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <div class="footer-logo-img">
              <img
                src="./images/growwell_logo_01.webp"
                alt="Growwell Logo"
                loading="lazy"
                class="footer-logo-image"
              />
            </div>
            <p>Your partner for financial growth and security</p>
            <div class="social-icons">
              <a
                href="https://www.facebook.com/growwellimf/"
                target="/"
                aria-label="Follow us on Facebook"
                ><i class="fab fa-facebook-f"></i
              ></a>
              <a
                href="https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08"
                target="/"
                aria-label="Follow us on X"
                ><i class="fab fa-twitter"></i
              ></a>
              <a
                href="https://www.linkedin.com/company/grow-well-imf/about/"
                target="/"
                aria-label="Follow us on Linkedin"
                ><i class="fab fa-linkedin-in"></i
              ></a>
              <a
                href="https://www.instagram.com/growwell.imf/"
                target="/"
                aria-label="Follow us on Instagram"
                ><i class="fab fa-instagram"></i
              ></a>
            </div>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h3>Quick Links</h3>
              <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="./investment-plans.html">Products</a></li>
                <li><a href="./renew-policy.html">Renew</a></li>
                <li><a href="./advisor.html">Testimonials</a></li>
                <li><a href="./contact.html">Contact</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Services</h3>
              <ul>
                <li><a href="investment-plans.html">Financial Planning</a></li>
                <li><a href="child-plans.html">Child Planning</a></li>
                <li><a href="investment-plans.html">Investment Planning</a></li>
                <li><a href="retirement-plans.html">Retirement Planning</a></li>
                <li><a href="term-insurance.html">Term Insurance</a></li>
                <li><a href="#">Tax Advisory</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Resources</h3>
              <ul>
                <li><a href="leadership.html">Leadership</a></li>
                <li><a href="advisor.html">Advisor</a></li>
                <li>
                  <a href="retirement-plans.html">Financial Calculators</a>
                </li>
                <li><a href="faqs.html">FAQs</a></li>
                <li><a href="contact.html">contact</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2023 Growwell Financial Advisors. All rights reserved.</p>
          <ul class="footer-legal">
            <li><a href="privacy-policy.html">Privacy Policy</a></li>
            <li><a href="terms-of-service.html">Terms of Service</a></li>
            <li><a href="cookie-policy.html">Cookie Policy</a></li>
          </ul>
        </div>
      </div>
    </footer>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Growwell",
        "url": "https://growwellimf.com",
        "logo": "https://growwellimf.com/images/growwell_logo_01.webp",
        "sameAs": [
          "https://www.facebook.com/growwellimf/",
          "https://www.instagram.com/growwell.imf/",
          "https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08",
          "https://www.linkedin.com/company/grow-well-imf/about/"
        ]
      }
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Mobile Menu Toggle
        const hamburger = document.getElementById("hamburger");
        const navMenu = document.getElementById("nav-menu");
        const body = document.body;

        if (hamburger) {
          hamburger.addEventListener("click", function () {
            navMenu.classList.toggle("active");
            body.classList.toggle("nav-active");

            // Toggle hamburger appearance
            const bars = this.querySelectorAll(".bar");
            bars.forEach((bar) => bar.classList.toggle("active"));

            console.log(
              "Hamburger clicked. Nav active:",
              navMenu.classList.contains("active")
            );
          });
        }

        // Dropdown Toggle for Mobile
        const dropdownToggles = document.querySelectorAll(".dropdown-toggle");
        dropdownToggles.forEach((toggle) => {
          toggle.addEventListener("click", function (e) {
            // Only run on mobile (width <= 992px)
            if (window.innerWidth <= 992) {
              e.preventDefault();
              e.stopPropagation();

              // Find the dropdown menu that is a sibling of this toggle
              const dropdownMenu = this.nextElementSibling;

              // Close all other dropdown menus
              document.querySelectorAll(".dropdown-menu").forEach((menu) => {
                if (menu !== dropdownMenu) {
                  menu.classList.remove("active");
                }
              });

              // Remove active class from all other toggles
              document.querySelectorAll(".dropdown-toggle").forEach((tog) => {
                if (tog !== this) {
                  tog.classList.remove("active");
                }
              });

              // Toggle active class on this dropdown menu and toggle
              dropdownMenu.classList.toggle("active");
              this.classList.toggle("active");
            }
          });
        });

        // Close menu when clicking outside
        document.addEventListener("click", function (e) {
          if (window.innerWidth <= 992) {
            if (!e.target.closest("nav") && !e.target.closest("#hamburger")) {
              // Close the navigation menu
              if (navMenu.classList.contains("active")) {
                navMenu.classList.remove("active");
                body.classList.remove("nav-active");

                // Reset hamburger icon
                const bars = hamburger.querySelectorAll(".bar");
                bars.forEach((bar) => bar.classList.remove("active"));

                // Close all dropdown menus
                document.querySelectorAll(".dropdown-menu").forEach((menu) => {
                  menu.classList.remove("active");
                });

                // Remove active class from all toggles
                document.querySelectorAll(".dropdown-toggle").forEach((tog) => {
                  tog.classList.remove("active");
                });
              }
            }
          }
        });

        // FAQ toggle functionality
        const faqItems = document.querySelectorAll(".faq-item");
        faqItems.forEach((item) => {
          const question = item.querySelector(".faq-question");
          if (question) {
            question.addEventListener("click", function () {
              item.classList.toggle("active");
            });
          }
        });

        // Support ticket form submission
        const supportForm = document.getElementById("supportForm");
        const ticketSuccess = document.getElementById("ticketSuccess");
        const ticketNumber = document.getElementById("ticketNumber");

        if (supportForm) {
          supportForm.addEventListener("submit", function (e) {
            e.preventDefault();

            // Generate random ticket number for demo
            const randomTicket = Math.floor(100000 + Math.random() * 900000);
            ticketNumber.textContent = randomTicket;

            // Show success message
            ticketSuccess.style.display = "block";

            // Scroll to success message
            ticketSuccess.scrollIntoView({ behavior: "smooth" });

            // Reset form
            supportForm.reset();
          });
        }
      });
    </script>

    <!-- User Guides Modal -->
    <div id="guidesModal" class="support-modal">
      <div class="support-modal-content">
        <div class="support-modal-header">
          <h2><i class="fas fa-book"></i> User Guides</h2>
          <span class="support-close" onclick="closeModal('guidesModal')">&times;</span>
        </div>
        <div class="support-modal-body">
          <div class="guides-grid">
            <div class="guide-item">
              <div class="guide-icon">
                <i class="fas fa-user-plus"></i>
              </div>
              <div class="guide-content">
                <h4>Getting Started Guide</h4>
                <p>Learn how to create your account, set up your profile, and navigate the platform basics.</p>
                <a href="#" class="guide-link" onclick="openGuideContent('getting-started')">Read Guide <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>

            <div class="guide-item">
              <div class="guide-icon">
                <i class="fas fa-chart-line"></i>
              </div>
              <div class="guide-content">
                <h4>Investment Planning</h4>
                <p>Step-by-step instructions for creating and managing your investment portfolio.</p>
                <a href="#" class="guide-link" onclick="openGuideContent('investment-planning')">Read Guide <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>

            <div class="guide-item">
              <div class="guide-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <div class="guide-content">
                <h4>Insurance Products</h4>
                <p>Complete guide to understanding and selecting the right insurance products for you.</p>
                <a href="#" class="guide-link" onclick="openGuideContent('insurance-products')">Read Guide <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>

            <div class="guide-item">
              <div class="guide-icon">
                <i class="fas fa-mobile-alt"></i>
              </div>
              <div class="guide-content">
                <h4>Mobile App Guide</h4>
                <p>Learn how to use our mobile app features for managing your finances on the go.</p>
                <a href="#" class="guide-link" onclick="openGuideContent('mobile-app')">Read Guide <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>

            <div class="guide-item">
              <div class="guide-icon">
                <i class="fas fa-cog"></i>
              </div>
              <div class="guide-content">
                <h4>Account Settings</h4>
                <p>Manage your account preferences, security settings, and notification options.</p>
                <a href="#" class="guide-link" onclick="openGuideContent('account-settings')">Read Guide <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>

            <div class="guide-item">
              <div class="guide-icon">
                <i class="fas fa-question-circle"></i>
              </div>
              <div class="guide-content">
                <h4>Troubleshooting</h4>
                <p>Common issues and their solutions to help you resolve problems quickly.</p>
                <a href="#" class="guide-link" onclick="openGuideContent('troubleshooting')">Read Guide <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Video Tutorials Modal -->
    <div id="videosModal" class="support-modal">
      <div class="support-modal-content">
        <div class="support-modal-header">
          <h2><i class="fas fa-video"></i> Video Tutorials</h2>
          <span class="support-close" onclick="closeModal('videosModal')">&times;</span>
        </div>
        <div class="support-modal-body">
          <div class="videos-grid">
            <div class="video-item" onclick="playVideo('platform-overview')">
              <div class="video-thumbnail">
                <i class="fas fa-play-circle"></i>
                <div class="video-duration">5:30</div>
              </div>
              <div class="video-content">
                <h4>Platform Overview</h4>
                <p>Get a comprehensive overview of our platform and its key features.</p>
                <div class="video-meta">
                  <span><i class="fas fa-eye"></i> 2.5K views</span>
                  <span><i class="fas fa-clock"></i> 5 min</span>
                </div>
              </div>
            </div>

            <div class="video-item" onclick="playVideo('first-investment')">
              <div class="video-thumbnail">
                <i class="fas fa-play-circle"></i>
                <div class="video-duration">8:15</div>
              </div>
              <div class="video-content">
                <h4>Creating Your First Investment</h4>
                <p>Learn how to make your first investment with our easy-to-follow tutorial.</p>
                <div class="video-meta">
                  <span><i class="fas fa-eye"></i> 1.8K views</span>
                  <span><i class="fas fa-clock"></i> 8 min</span>
                </div>
              </div>
            </div>

            <div class="video-item" onclick="playVideo('portfolio-management')">
              <div class="video-thumbnail">
                <i class="fas fa-play-circle"></i>
                <div class="video-duration">6:45</div>
              </div>
              <div class="video-content">
                <h4>Portfolio Management</h4>
                <p>Discover how to effectively manage and track your investment portfolio.</p>
                <div class="video-meta">
                  <span><i class="fas fa-eye"></i> 3.2K views</span>
                  <span><i class="fas fa-clock"></i> 6 min</span>
                </div>
              </div>
            </div>

            <div class="video-item" onclick="playVideo('mobile-app')">
              <div class="video-thumbnail">
                <i class="fas fa-play-circle"></i>
                <div class="video-duration">4:20</div>
              </div>
              <div class="video-content">
                <h4>Mobile App Tutorial</h4>
                <p>Master the mobile app features for managing your finances anywhere.</p>
                <div class="video-meta">
                  <span><i class="fas fa-eye"></i> 1.5K views</span>
                  <span><i class="fas fa-clock"></i> 4 min</span>
                </div>
              </div>
            </div>

            <div class="video-item" onclick="playVideo('tax-planning')">
              <div class="video-thumbnail">
                <i class="fas fa-play-circle"></i>
                <div class="video-duration">7:10</div>
              </div>
              <div class="video-content">
                <h4>Tax Planning Strategies</h4>
                <p>Learn effective tax planning strategies to maximize your returns.</p>
                <div class="video-meta">
                  <span><i class="fas fa-eye"></i> 2.1K views</span>
                  <span><i class="fas fa-clock"></i> 7 min</span>
                </div>
              </div>
            </div>

            <div class="video-item" onclick="playVideo('risk-assessment')">
              <div class="video-thumbnail">
                <i class="fas fa-play-circle"></i>
                <div class="video-duration">9:30</div>
              </div>
              <div class="video-content">
                <h4>Risk Assessment Guide</h4>
                <p>Understand how to assess and manage investment risks effectively.</p>
                <div class="video-meta">
                  <span><i class="fas fa-eye"></i> 1.9K views</span>
                  <span><i class="fas fa-clock"></i> 9 min</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Documents Modal -->
    <div id="documentsModal" class="support-modal">
      <div class="support-modal-content">
        <div class="support-modal-header">
          <h2><i class="fas fa-file-alt"></i> Document Center</h2>
          <span class="support-close" onclick="closeModal('documentsModal')">&times;</span>
        </div>
        <div class="support-modal-body">
          <div class="documents-categories">
            <div class="doc-category">
              <div class="doc-category-header">
                <i class="fas fa-folder"></i>
                <h4>Investment Documents</h4>
              </div>
              <div class="doc-list">
                <div class="doc-item">
                  <div class="doc-info">
                    <i class="fas fa-file-pdf"></i>
                    <div>
                      <h5>Investment Plan Brochure</h5>
                      <span>PDF • 2.5 MB • Updated: Dec 2024</span>
                    </div>
                  </div>
                  <a href="#" class="doc-download" onclick="downloadDocument('investment-brochure.pdf')"><i class="fas fa-download"></i></a>
                </div>

                <div class="doc-item">
                  <div class="doc-info">
                    <i class="fas fa-file-pdf"></i>
                    <div>
                      <h5>Portfolio Performance Report</h5>
                      <span>PDF • 1.8 MB • Updated: Dec 2024</span>
                    </div>
                  </div>
                  <a href="#" class="doc-download" onclick="downloadDocument('portfolio-report.pdf')"><i class="fas fa-download"></i></a>
                </div>

                <div class="doc-item">
                  <div class="doc-info">
                    <i class="fas fa-file-excel"></i>
                    <div>
                      <h5>Investment Calculator Template</h5>
                      <span>XLSX • 0.5 MB • Updated: Nov 2024</span>
                    </div>
                  </div>
                  <a href="#" class="doc-download" onclick="downloadDocument('investment-calculator.xlsx')"><i class="fas fa-download"></i></a>
                </div>
              </div>
            </div>

            <div class="doc-category">
              <div class="doc-category-header">
                <i class="fas fa-folder"></i>
                <h4>Insurance Documents</h4>
              </div>
              <div class="doc-list">
                <div class="doc-item">
                  <div class="doc-info">
                    <i class="fas fa-file-pdf"></i>
                    <div>
                      <h5>Term Insurance Policy Details</h5>
                      <span>PDF • 3.2 MB • Updated: Dec 2024</span>
                    </div>
                  </div>
                  <a href="#" class="doc-download" onclick="downloadDocument('term-insurance-policy.pdf')"><i class="fas fa-download"></i></a>
                </div>

                <div class="doc-item">
                  <div class="doc-info">
                    <i class="fas fa-file-pdf"></i>
                    <div>
                      <h5>Child Plan Benefits Guide</h5>
                      <span>PDF • 2.1 MB • Updated: Nov 2024</span>
                    </div>
                  </div>
                  <a href="#" class="doc-download" onclick="downloadDocument('child-plan-guide.pdf')"><i class="fas fa-download"></i></a>
                </div>

                <div class="doc-item">
                  <div class="doc-info">
                    <i class="fas fa-file-word"></i>
                    <div>
                      <h5>Claim Form Template</h5>
                      <span>DOCX • 0.3 MB • Updated: Oct 2024</span>
                    </div>
                  </div>
                  <a href="#" class="doc-download" onclick="downloadDocument('claim-form.docx')"><i class="fas fa-download"></i></a>
                </div>
              </div>
            </div>

            <div class="doc-category">
              <div class="doc-category-header">
                <i class="fas fa-folder"></i>
                <h4>Legal & Compliance</h4>
              </div>
              <div class="doc-list">
                <div class="doc-item">
                  <div class="doc-info">
                    <i class="fas fa-file-pdf"></i>
                    <div>
                      <h5>Terms & Conditions</h5>
                      <span>PDF • 1.2 MB • Updated: Dec 2024</span>
                    </div>
                  </div>
                  <a href="#" class="doc-download" onclick="downloadDocument('terms-conditions.pdf')"><i class="fas fa-download"></i></a>
                </div>

                <div class="doc-item">
                  <div class="doc-info">
                    <i class="fas fa-file-pdf"></i>
                    <div>
                      <h5>Privacy Policy</h5>
                      <span>PDF • 0.8 MB • Updated: Nov 2024</span>
                    </div>
                  </div>
                  <a href="#" class="doc-download" onclick="downloadDocument('privacy-policy.pdf')"><i class="fas fa-download"></i></a>
                </div>

                <div class="doc-item">
                  <div class="doc-info">
                    <i class="fas fa-file-pdf"></i>
                    <div>
                      <h5>Regulatory Compliance Guide</h5>
                      <span>PDF • 2.7 MB • Updated: Oct 2024</span>
                    </div>
                  </div>
                  <a href="#" class="doc-download" onclick="downloadDocument('compliance-guide.pdf')"><i class="fas fa-download"></i></a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Modal Functions
      function openGuidesModal() {
        document.getElementById('guidesModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
      }

      function openVideosModal() {
        document.getElementById('videosModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
      }

      function openDocumentsModal() {
        document.getElementById('documentsModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
      }

      function closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
        document.body.style.overflow = 'auto';
      }

      // Close modal when clicking outside
      window.onclick = function(event) {
        const modals = ['guidesModal', 'videosModal', 'documentsModal'];
        modals.forEach(modalId => {
          const modal = document.getElementById(modalId);
          if (event.target === modal) {
            closeModal(modalId);
          }
        });
      }

      // Close modal with Escape key
      document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
          const modals = ['guidesModal', 'videosModal', 'documentsModal'];
          modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (modal.style.display === 'block') {
              closeModal(modalId);
            }
          });
        }
      });

      // Guide Content Function
      function openGuideContent(guideType) {
        const guideData = {
          'getting-started': {
            title: 'Getting Started Guide',
            content: `
              <h3>Welcome to GrowWell Financial Services!</h3>
              <p>This comprehensive guide will help you get started with our platform.</p>

              <h4>Step 1: Account Setup</h4>
              <ul>
                <li>Complete your profile information</li>
                <li>Verify your email address</li>
                <li>Set up two-factor authentication</li>
                <li>Upload required documents</li>
              </ul>

              <h4>Step 2: Dashboard Overview</h4>
              <ul>
                <li>Navigate the main dashboard</li>
                <li>Understand your portfolio summary</li>
                <li>Access quick actions menu</li>
                <li>Review recent transactions</li>
              </ul>

              <h4>Step 3: Making Your First Investment</h4>
              <ul>
                <li>Browse available investment options</li>
                <li>Use our risk assessment tool</li>
                <li>Set your investment goals</li>
                <li>Complete your first transaction</li>
              </ul>

              <div class="guide-tips">
                <h4>💡 Pro Tips:</h4>
                <ul>
                  <li>Start with small investments to familiarize yourself</li>
                  <li>Use our mobile app for on-the-go management</li>
                  <li>Set up automatic notifications for important updates</li>
                </ul>
              </div>
            `
          },
          'investment-planning': {
            title: 'Investment Planning Guide',
            content: `
              <h3>Smart Investment Planning Strategies</h3>
              <p>Learn how to create and manage a successful investment portfolio.</p>

              <h4>Understanding Risk Tolerance</h4>
              <ul>
                <li>Assess your financial goals</li>
                <li>Determine your time horizon</li>
                <li>Evaluate your risk capacity</li>
                <li>Complete our risk assessment questionnaire</li>
              </ul>

              <h4>Diversification Strategies</h4>
              <ul>
                <li>Asset allocation principles</li>
                <li>Geographic diversification</li>
                <li>Sector-wise distribution</li>
                <li>Rebalancing your portfolio</li>
              </ul>

              <h4>Investment Options Available</h4>
              <ul>
                <li>Mutual Funds - Equity, Debt, Hybrid</li>
                <li>Direct Equity Investments</li>
                <li>Fixed Deposits and Bonds</li>
                <li>ELSS for Tax Saving</li>
              </ul>

              <div class="guide-tips">
                <h4>📈 Investment Tips:</h4>
                <ul>
                  <li>Start early to benefit from compounding</li>
                  <li>Invest regularly through SIP</li>
                  <li>Review and rebalance quarterly</li>
                </ul>
              </div>
            `
          },
          'insurance-products': {
            title: 'Insurance Products Guide',
            content: `
              <h3>Comprehensive Insurance Solutions</h3>
              <p>Protect your family's future with our range of insurance products.</p>

              <h4>Term Life Insurance</h4>
              <ul>
                <li>High coverage at affordable premiums</li>
                <li>Flexible policy terms (10-40 years)</li>
                <li>Online application process</li>
                <li>Quick claim settlement</li>
              </ul>

              <h4>Child Education Plans</h4>
              <ul>
                <li>Guaranteed returns for education expenses</li>
                <li>Waiver of premium benefit</li>
                <li>Flexible payout options</li>
                <li>Tax benefits under Section 80C</li>
              </ul>

              <h4>Health Insurance</h4>
              <ul>
                <li>Comprehensive medical coverage</li>
                <li>Cashless treatment at network hospitals</li>
                <li>Pre and post hospitalization coverage</li>
                <li>Annual health check-ups included</li>
              </ul>

              <div class="guide-tips">
                <h4>🛡️ Insurance Tips:</h4>
                <ul>
                  <li>Buy insurance early for lower premiums</li>
                  <li>Choose coverage based on your income</li>
                  <li>Review your policies annually</li>
                </ul>
              </div>
            `
          },
          'mobile-app': {
            title: 'Mobile App Guide',
            content: `
              <h3>GrowWell Mobile App Features</h3>
              <p>Manage your finances on the go with our feature-rich mobile application.</p>

              <h4>Key Features</h4>
              <ul>
                <li>Real-time portfolio tracking</li>
                <li>One-click investment options</li>
                <li>Secure biometric login</li>
                <li>Push notifications for important updates</li>
              </ul>

              <h4>Investment Management</h4>
              <ul>
                <li>Start/stop SIP investments</li>
                <li>Switch between fund options</li>
                <li>Track performance with charts</li>
                <li>Download statements and reports</li>
              </ul>

              <h4>Quick Actions</h4>
              <ul>
                <li>Pay insurance premiums</li>
                <li>Update personal information</li>
                <li>Contact customer support</li>
                <li>Schedule consultation calls</li>
              </ul>

              <div class="guide-tips">
                <h4>📱 App Tips:</h4>
                <ul>
                  <li>Enable notifications for market updates</li>
                  <li>Use fingerprint login for quick access</li>
                  <li>Set up automatic backups</li>
                </ul>
              </div>
            `
          },
          'account-settings': {
            title: 'Account Settings Guide',
            content: `
              <h3>Manage Your Account Settings</h3>
              <p>Customize your account preferences and security settings.</p>

              <h4>Profile Management</h4>
              <ul>
                <li>Update personal information</li>
                <li>Change contact details</li>
                <li>Upload profile picture</li>
                <li>Manage bank account details</li>
              </ul>

              <h4>Security Settings</h4>
              <ul>
                <li>Change login password</li>
                <li>Enable two-factor authentication</li>
                <li>Set up security questions</li>
                <li>Review login history</li>
              </ul>

              <h4>Notification Preferences</h4>
              <ul>
                <li>Email notification settings</li>
                <li>SMS alert preferences</li>
                <li>Push notification controls</li>
                <li>Marketing communication options</li>
              </ul>

              <div class="guide-tips">
                <h4>🔒 Security Tips:</h4>
                <ul>
                  <li>Use strong, unique passwords</li>
                  <li>Enable 2FA for extra security</li>
                  <li>Regularly review account activity</li>
                </ul>
              </div>
            `
          },
          'troubleshooting': {
            title: 'Troubleshooting Guide',
            content: `
              <h3>Common Issues & Solutions</h3>
              <p>Quick solutions to frequently encountered problems.</p>

              <h4>Login Issues</h4>
              <ul>
                <li><strong>Forgot Password:</strong> Use 'Forgot Password' link on login page</li>
                <li><strong>Account Locked:</strong> Contact support after 3 failed attempts</li>
                <li><strong>2FA Problems:</strong> Use backup codes or contact support</li>
              </ul>

              <h4>Transaction Issues</h4>
              <ul>
                <li><strong>Payment Failed:</strong> Check bank balance and try again</li>
                <li><strong>SIP Not Processed:</strong> Ensure sufficient balance on due date</li>
                <li><strong>Redemption Delays:</strong> Allow 2-3 business days for processing</li>
              </ul>

              <h4>Technical Issues</h4>
              <ul>
                <li><strong>App Crashes:</strong> Update to latest version</li>
                <li><strong>Slow Loading:</strong> Check internet connection</li>
                <li><strong>Data Not Syncing:</strong> Logout and login again</li>
              </ul>

              <div class="guide-tips">
                <h4>🔧 Quick Fixes:</h4>
                <ul>
                  <li>Clear browser cache for web issues</li>
                  <li>Restart app for mobile problems</li>
                  <li>Contact support for persistent issues</li>
                </ul>
              </div>
            `
          }
        };

        const guide = guideData[guideType];
        if (guide) {
          alert(`${guide.title}\n\n${guide.content.replace(/<[^>]*>/g, '').replace(/&[^;]*;/g, ' ')}`);
        }
      }

      // Video Playback Function
      function playVideo(videoType) {
        const videoData = {
          'platform-overview': {
            title: 'Platform Overview',
            description: 'Welcome to GrowWell! This video provides a comprehensive overview of our platform features, dashboard navigation, and key functionalities. Learn how to make the most of your financial journey with us.',
            duration: '5:30',
            content: 'This video would cover:\n• Dashboard walkthrough\n• Navigation menu overview\n• Key features introduction\n• Account setup process\n• Getting started tips'
          },
          'first-investment': {
            title: 'Creating Your First Investment',
            description: 'Step-by-step tutorial on making your first investment. From selecting the right product to completing the transaction, this video guides you through the entire process.',
            duration: '8:15',
            content: 'This video would cover:\n• Investment product selection\n• Risk assessment process\n• Amount calculation\n• Payment methods\n• Confirmation and tracking'
          },
          'portfolio-management': {
            title: 'Portfolio Management',
            description: 'Learn advanced portfolio management techniques including diversification, rebalancing, and performance tracking to optimize your investment returns.',
            duration: '6:45',
            content: 'This video would cover:\n• Portfolio analysis tools\n• Asset allocation strategies\n• Rebalancing techniques\n• Performance metrics\n• Risk management'
          },
          'mobile-app': {
            title: 'Mobile App Tutorial',
            description: 'Discover the power of our mobile app. Learn how to manage investments, track performance, and access all features from your smartphone.',
            duration: '4:20',
            content: 'This video would cover:\n• App installation and setup\n• Mobile dashboard features\n• Investment management on mobile\n• Security features\n• Notification settings'
          },
          'tax-planning': {
            title: 'Tax Planning Strategies',
            description: 'Maximize your tax savings with smart investment strategies. Learn about ELSS, tax-saving instruments, and optimal planning techniques.',
            duration: '7:10',
            content: 'This video would cover:\n• Tax-saving investment options\n• ELSS fund selection\n• Section 80C benefits\n• Tax harvesting strategies\n• Year-end planning'
          },
          'risk-assessment': {
            title: 'Risk Assessment Guide',
            description: 'Understand investment risks and learn how to assess your risk tolerance. Make informed decisions based on your financial goals and risk capacity.',
            duration: '9:30',
            content: 'This video would cover:\n• Types of investment risks\n• Risk tolerance assessment\n• Risk-return relationship\n• Diversification benefits\n• Risk management tools'
          }
        };

        const video = videoData[videoType];
        if (video) {
          alert(`🎥 ${video.title} (${video.duration})\n\n${video.description}\n\n${video.content}\n\n[This is a sample video description. In a real implementation, this would open a video player or redirect to the actual video content.]`);
        }
      }

      // Document Download Function
      function downloadDocument(fileName) {
        const documentData = {
          'investment-brochure.pdf': {
            name: 'Investment Plan Brochure',
            size: '2.5 MB',
            type: 'PDF',
            description: 'Comprehensive guide to our investment products including mutual funds, equity options, and fixed income instruments.'
          },
          'portfolio-report.pdf': {
            name: 'Portfolio Performance Report',
            size: '1.8 MB',
            type: 'PDF',
            description: 'Detailed analysis of portfolio performance, asset allocation, and investment recommendations.'
          },
          'investment-calculator.xlsx': {
            name: 'Investment Calculator Template',
            size: '0.5 MB',
            type: 'Excel',
            description: 'Interactive spreadsheet to calculate investment returns, SIP amounts, and goal-based planning.'
          },
          'term-insurance-policy.pdf': {
            name: 'Term Insurance Policy Details',
            size: '3.2 MB',
            type: 'PDF',
            description: 'Complete policy document with terms, conditions, benefits, and claim procedures.'
          },
          'child-plan-guide.pdf': {
            name: 'Child Plan Benefits Guide',
            size: '2.1 MB',
            type: 'PDF',
            description: 'Detailed guide on child education plans, benefits, and investment strategies for your child\'s future.'
          },
          'claim-form.docx': {
            name: 'Claim Form Template',
            size: '0.3 MB',
            type: 'Word Document',
            description: 'Standard claim form template for insurance claims with step-by-step filling instructions.'
          },
          'terms-conditions.pdf': {
            name: 'Terms & Conditions',
            size: '1.2 MB',
            type: 'PDF',
            description: 'Legal terms and conditions governing the use of our services and platform.'
          },
          'privacy-policy.pdf': {
            name: 'Privacy Policy',
            size: '0.8 MB',
            type: 'PDF',
            description: 'Our commitment to protecting your privacy and how we handle your personal information.'
          },
          'compliance-guide.pdf': {
            name: 'Regulatory Compliance Guide',
            size: '2.7 MB',
            type: 'PDF',
            description: 'Information about regulatory compliance, SEBI guidelines, and investor protection measures.'
          }
        };

        const doc = documentData[fileName];
        if (doc) {
          // Simulate download process
          alert(`📄 Downloading: ${doc.name}\n\nFile Type: ${doc.type}\nFile Size: ${doc.size}\n\nDescription: ${doc.description}\n\n[This is a sample download. In a real implementation, the actual file would be downloaded to your device.]`);

          // You could also create a temporary download link here
          // const link = document.createElement('a');
          // link.href = 'path/to/actual/file/' + fileName;
          // link.download = fileName;
          // link.click();
        }
      }
    </script>

  </body>
</html>
