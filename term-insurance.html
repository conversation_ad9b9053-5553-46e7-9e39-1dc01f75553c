<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Affordable Term Insurance Plans | Growwell</title>
    <meta
      name="description"
      content="Protect your loved ones with Growwell affordable term insurance plans. Get high coverage, low premiums, and peace of mind for your family’s future."
    />
    <meta
      name="keywords"
      content="growwell, term insurance, life insurance, health insurance plans, insurance products, insurance company"
    />
    <meta name="robots" content="term-insurance, follow" />
    <link rel="canonical" href="https://growwellimf.com/" />
    <meta
      property="og:title"
      content="Affordable Term Insurance Plans | Growwell"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://growwellimf.com/" />
    <meta
      property="og:image"
      content="https://growwellimf.com/images/growwell_logo_01.webp"
    />
    <meta name="author" content="Growwell" />
    <meta name="publisher" content="Growwell" />

    <!--google adsense-->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>

    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/term-insurance.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Header Section -->
    <header>
      <div class="container">
        <div class="navbar">
          <div class="logo">
            <a href="index.html">
              <img
                src="./images/growwell_logo_01.webp"
                alt="company_logo"
                class="brand-logo"
            /></a>
            <a href="index.html"
              ><h2><span class="highlight">Grow</span>well</h2></a
            >
          </div>

          <div class="hamburger" id="hamburger">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
          </div>

          <nav id="nav-menu">
            <ul id="menu-items">
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Products</a>
                <div class="dropdown-menu">
                  <a href="investment-plans.html">Investment Plans</a>
                  <a href="retirement-plans.html">Retirement Plans</a>
                  <a href="child-plans.html">Child Plans</a>
                  <a href="term-insurance.html">Term Insurance</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Renew</a>
                <div class="dropdown-menu">
                  <a href="renew-policy.html">Renew Policy</a>
                  <a href="check-status.html">Check Status</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Claim</a>
                <div class="dropdown-menu">
                  <a href="file-claim.html">File a Claim</a>
                  <a href="track-claim.html">Track Claim</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Help & Support</a>
                <div class="dropdown-menu">
                  <a href="faqs.html">FAQs</a>
                  <a href="contact.html">Contact Us</a>
                  <a href="support.html">Customer Support</a>
                  <a href="advisor.html">Find an Advisor</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">About Us</a>
                <div class="dropdown-menu">
                  <a href="our-story.html">Our Story</a>
                  <a href="leadership.html">Leadership</a>
                  <a href="careers.html">Careers</a>
                </div>
              </li>
            </ul>
          </nav>

          <div class="navbar-actions desktop-only">
            <a href="investment-plans.html" class="btn-primary">Get Started</a>
          </div>
        </div>
      </div>
    </header>
    <!-- Hero Banner for Term Insurance -->
    <section class="term-hero">
      <div class="container">
        <div class="term-hero-content centered">
          <div class="term-hero-wrapper">
            <span class="hero-badge">Term Insurance</span>
            <h1>
              Secure Your Family's
              <span class="gradient-text">Financial Future</span>
            </h1>
            <p>
              Affordable term insurance `plans that provide comprehensive
              coverage for your loved ones. Get high coverage at low premiums
              with our range of term insurance solutions.
            </p>
            <div class="term-hero-cta">
              <a href="#calculator" class="btn-primary btn-hero"
                >Calculate Premium</a
              >
              <a href="#plans" class="btn-secondary btn-hero">Explore Plans</a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Key Benefits -->
    <section class="term-benefits">
      <div class="container">
        <div class="section-header centered">
          <span class="section-subtitle">Why Choose Us</span>
          <h2>Term Insurance <span class="benefits-txt"> Benefits</span></h2>
          <div class="title-underline"></div>
          <p>
            Comprehensive protection for your loved ones at affordable premiums
          </p>
        </div>

        <div class="benefits-grid">
          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <h3 class="benefit-title">High Coverage, Low Premium</h3>
            <p class="benefit-description">
              Get substantial life coverage starting at just ₹699 per month.
              Secure your family's future without straining your finances.
            </p>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-coins"></i>
            </div>
            <h3 class="benefit-title">Tax Benefits</h3>
            <p class="benefit-description">
              Enjoy tax benefits on premiums paid under Section 80C and tax-free
              death benefits under Section 10(10D) of the Income Tax Act.
            </p>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-hand-holding-heart"></i>
            </div>
            <h3 class="benefit-title">Multiple Payout Options</h3>
            <p class="benefit-description">
              Choose from lump sum, monthly income, or a combination of both to
              ensure your family receives benefits according to their needs.
            </p>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-umbrella"></i>
            </div>
            <h3 class="benefit-title">Additional Riders</h3>
            <p class="benefit-description">
              Enhance your protection with optional riders like critical
              illness, accidental death benefit, and disability cover.
            </p>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-medal"></i>
            </div>
            <h3 class="benefit-title">Claim Settlement Ratio</h3>
            <p class="benefit-description">
              With a 99.4% claim settlement ratio, we ensure your family
              receives the benefits when they need them the most.
            </p>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-bolt"></i>
            </div>
            <h3 class="benefit-title">Instant Approval</h3>
            <p class="benefit-description">
              Get instant approval with minimal documentation. Our digital-first
              approach ensures quick processing and approval.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Term Insurance Plans -->
    <section id="plans" class="term-plans">
      <div class="container">
        <div class="section-header centered">
          <span class="section-subtitle">Our Offerings</span>
          <h2><span class="benefits-txt">Term</span> Insurance Plans</h2>
          <div class="title-underline"></div>
          <p>Choose the plan that best suits your protection needs</p>
        </div>

        <div class="plans-grid">
          <div class="plan-card">
            <div class="plan-badge">Basic</div>
            <div class="plan-header">
              <h3 class="plan-name">Growwell Term Shield</h3>
              <p class="plan-description">
                Essential coverage for your family's protection needs at an
                affordable premium.
              </p>
              <div class="plan-price">
                ₹699<span class="plan-duration">/month</span>
              </div>
            </div>
            <ul class="plan-features">
              <li><i class="fas fa-check"></i> Coverage up to ₹1 Crore</li>
              <li><i class="fas fa-check"></i> Term up to 40 years</li>
              <li><i class="fas fa-check"></i> Lump sum payout</li>
              <li>
                <i class="fas fa-check"></i> Tax benefits under Section 80C
              </li>
              <li><i class="fas fa-check"></i> 30-day free look period</li>
              <li><i class="fas fa-check"></i> Premium waiver on disability</li>
              <li>
                <i class="fas fa-check"></i> Special rates for non-smokers
              </li>
              <li>
                <i class="fas fa-check"></i> Accidental death benefit included
              </li>
            </ul>
            <div class="plan-cta">
              <a href="#contact" class="btn-plan">Get Started</a>
            </div>
          </div>

          <div class="plan-card popular">
            <div class="plan-badge">Most Popular</div>
            <div class="plan-header">
              <h3 class="plan-name">Growwell Term Shield Plus</h3>
              <p class="plan-description">
                Comprehensive coverage with additional benefits for complete
                family protection.
              </p>
              <div class="plan-price">
                ₹999<span class="plan-duration">/month</span>
              </div>
            </div>
            <ul class="plan-features">
              <li><i class="fas fa-check"></i> Coverage up to ₹2 Crore</li>
              <li><i class="fas fa-check"></i> Term up to 50 years</li>
              <li>
                <i class="fas fa-check"></i> Lump sum or monthly income option
              </li>
              <li>
                <i class="fas fa-check"></i> Critical Illness rider included
              </li>
              <li>
                <i class="fas fa-check"></i> Accidental death benefit included
              </li>
              <li><i class="fas fa-check"></i> Premium waiver on disability</li>
              <li>
                <i class="fas fa-check"></i> Special rates for non-smokers
              </li>
            </ul>
            <div class="plan-cta">
              <a href="#contact" class="btn-plan">Get Started</a>
            </div>
          </div>

          <div class="plan-card">
            <div class="plan-badge">Premium</div>
            <div class="plan-header">
              <h3 class="plan-name">Growwell Term Shield Premier</h3>
              <p class="plan-description">
                Elite coverage with maximum benefits and flexible payout
                options.
              </p>
              <div class="plan-price">
                ₹1,499<span class="plan-duration">/month</span>
              </div>
            </div>
            <ul class="plan-features">
              <li><i class="fas fa-check"></i> Coverage up to ₹5 Crore</li>
              <li><i class="fas fa-check"></i> Term up to 60 years</li>
              <li><i class="fas fa-check"></i> Flexible payout options</li>
              <li>
                <i class="fas fa-check"></i> Comprehensive critical illness
                cover
              </li>
              <li><i class="fas fa-check"></i> Return of premium option</li>
              <li>
                <i class="fas fa-check"></i> Coverage for 40+ critical illnesses
              </li>
              <li><i class="fas fa-check"></i> International coverage</li>
              <li>
                <i class="fas fa-check"></i> Special rates for healthy
                lifestyles
              </li>
            </ul>
            <div class="plan-cta">
              <a href="#contact" class="btn-plan">Get Started</a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Premium Calculator -->
    <section id="calculator" class="term-calculator">
      <div class="container">
        <div class="section-header centered">
          <span class="section-subtitle">Premium Estimator</span>
          <h2>Calculate Your <span class="benefits-txt">Premium</span></h2>
          <div class="title-underline"></div>
          <p>Get an instant estimate of your term insurance premium</p>
        </div>

        <div class="calculator-wrapper">
          <div class="calculator-form">
            <div class="calculator-header">
              <h3>Enter Your Details</h3>
              <p>Provide your information for an accurate premium estimate</p>
            </div>
            <form id="premium-calculator">
              <div class="form-group">
                <label for="age">Your Age</label>
                <input
                  type="number"
                  id="age"
                  class="form-control"
                  min="18"
                  max="65"
                  value="30"
                />
                <input
                  type="range"
                  id="age-slider"
                  class="range-slider"
                  min="18"
                  max="65"
                  value="30"
                />
              </div>

              <div class="form-group">
                <label for="gender">Gender</label>
                <select id="gender" class="form-control">
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                </select>
              </div>

              <div class="form-group">
                <label for="coverage">Coverage Amount (₹)</label>
                <select id="coverage" class="form-control">
                  <option value="5000000">₹50 Lakhs</option>
                  <option value="10000000" selected>₹1 Crore</option>
                  <option value="20000000">₹2 Crore</option>
                  <option value="50000000">₹5 Crore</option>
                </select>
              </div>

              <div class="form-group">
                <label for="term">Policy Term (Years)</label>
                <input
                  type="number"
                  id="term"
                  class="form-control"
                  min="5"
                  max="40"
                  value="30"
                />
                <input
                  type="range"
                  id="term-slider"
                  class="range-slider"
                  min="5"
                  max="40"
                  value="30"
                />
              </div>

              <div class="form-group">
                <label for="smoker">Do you smoke?</label>
                <select id="smoker" class="form-control">
                  <option value="no">No</option>
                  <option value="yes">Yes</option>
                </select>
              </div>

              <button
                type="button"
                id="calculate-btn"
                class="btn-primary premium-button"
              >
                Calculate Premium
              </button>
            </form>
          </div>

          <div class="calculator-result">
            <div class="result-header">
              <h3>Your Premium Estimate</h3>
              <p>Based on the information you provided</p>
            </div>

            <div class="result-card">
              <div class="result-label">Monthly Premium</div>
              <div class="result-value">₹899</div>
            </div>

            <div class="result-card">
              <div class="result-label">Annual Premium</div>
              <div class="result-value">₹10,788</div>
            </div>

            <div class="result-card">
              <div class="result-label">Tax Benefit (Sec 80C)</div>
              <div class="result-value">₹2,158</div>
            </div>

            <button class="calc-btn">Talk to an Advisor</button>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
      <div class="container">
        <div class="section-header centered">
          <span class="section-subtitle">Common Questions</span>
          <h2>Frequently Asked <span class="benefits-txt">Questions</span></h2>
          <div class="title-underline"></div>
        </div>

        <div class="faq-wrapper">
          <div class="faq-item active">
            <div class="faq-question">
              <h4>What is term insurance?</h4>
              <span class="icon"><i class="fas fa-chevron-down"></i></span>
            </div>
            <div class="faq-answer">
              <div class="faq-answer-inner">
                Term insurance is a pure protection plan that provides financial
                security to your family in case of your unfortunate demise
                during the policy term. It offers high coverage at affordable
                premiums with no maturity benefits if the policyholder survives
                the term.
              </div>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <h4>How much term insurance coverage do I need?</h4>
              <span class="icon"><i class="fas fa-chevron-down"></i></span>
            </div>
            <div class="faq-answer">
              <div class="faq-answer-inner">
                As a thumb rule, your coverage should be at least 10-15 times
                your annual income. However, the exact amount depends on your
                financial obligations, liabilities, dependent family members,
                and future goals. Our advisors can help you determine the right
                coverage based on your specific needs.
              </div>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <h4>What happens if I survive the policy term?</h4>
              <span class="icon"><i class="fas fa-chevron-down"></i></span>
            </div>
            <div class="faq-answer">
              <div class="faq-answer-inner">
                In a traditional term insurance plan, there are no maturity
                benefits if you survive the policy term. However, with our Term
                Shield Premier plan's Return of Premium option, you can get back
                all the premiums paid if you survive the policy term.
              </div>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <h4>Can I add riders to my term insurance plan?</h4>
              <span class="icon"><i class="fas fa-chevron-down"></i></span>
            </div>
            <div class="faq-answer">
              <div class="faq-answer-inner">
                Yes, you can enhance your term insurance coverage with riders
                like Critical Illness Cover, Accidental Death Benefit,
                Disability Benefit, and Premium Waiver benefit. These riders
                provide additional protection at a nominal extra cost.
              </div>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <h4>What documents are required to buy term insurance?</h4>
              <span class="icon"><i class="fas fa-chevron-down"></i></span>
            </div>
            <div class="faq-answer">
              <div class="faq-answer-inner">
                You need to submit identity proof (Aadhaar Card, PAN Card, Voter
                ID, etc.), address proof, age proof, income proof, and sometimes
                a medical examination report depending on your age and the
                coverage amount. For high-value policies, additional
                documentation may be required.
              </div>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <h4>What is not covered in term insurance?</h4>
              <span class="icon"><i class="fas fa-chevron-down"></i></span>
            </div>
            <div class="faq-answer">
              <div class="faq-answer-inner">
                Term insurance typically doesn't cover death due to suicide
                within the first year, death due to pre-existing undisclosed
                medical conditions, or death due to participation in hazardous
                activities not disclosed while purchasing the policy. It's
                important to disclose all relevant information while buying the
                policy.
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Trust Badges -->
    <section class="trust-badges">
      <div class="container">
        <div class="badges-container">
          <div class="badge-item">
            <div class="badge-icon-wrapper">
              <i class="fas fa-shield-alt"></i>
            </div>
            <p class="badge-text">IRDA Regulated</p>
          </div>

          <div class="badge-item">
            <div class="badge-icon-wrapper">
              <i class="fas fa-lock"></i>
            </div>
            <p class="badge-text">256-bit Secure Encryption</p>
          </div>

          <div class="badge-item">
            <div class="badge-icon-wrapper">
              <i class="fas fa-check-circle"></i>
            </div>
            <p class="badge-text">99.4% Claims Settled</p>
          </div>

          <div class="badge-item">
            <div class="badge-icon-wrapper">
              <i class="fas fa-users"></i>
            </div>
            <p class="badge-text">10M+ Happy Customers</p>
          </div>

          <div class="badge-item">
            <div class="badge-icon-wrapper">
              <i class="fas fa-headset"></i>
            </div>
            <p class="badge-text">24/7 Customer Support</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer Section -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <div class="footer-logo-img">
              <img
                src="./images/growwell_logo_01.webp"
                alt="Growwell Logo"
                loading="lazy"
                class="footer-logo-image"
              />
            </div>
            <p>Your partner for financial growth and security</p>
            <div class="social-icons">
              <a
                href="https://www.facebook.com/growwellimf/"
                target="/"
                aria-label="Follow us on Facebook"
                ><i class="fab fa-facebook-f"></i
              ></a>
              <a
                href="https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08"
                target="/"
                aria-label="Follow us on X"
                ><i class="fab fa-twitter"></i
              ></a>
              <a
                href="https://www.linkedin.com/company/grow-well-imf/about/"
                target="/"
                aria-label="Follow us on Linkedin"
                ><i class="fab fa-linkedin-in"></i
              ></a>
              <a
                href="https://www.instagram.com/growwell.imf/"
                target="/"
                aria-label="Follow us on Instagram"
                ><i class="fab fa-instagram"></i
              ></a>
            </div>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h3>Quick Links</h3>
              <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="./investment-plans.html">Products</a></li>
                <li><a href="./renew-policy.html">Renew</a></li>
                <li><a href="./advisor.html">Testimonials</a></li>
                <li><a href="./contact.html">Contact</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Services</h3>
              <ul>
                <li><a href="investment-plans.html">Financial Planning</a></li>
                <li><a href="child-plans.html">Child Planning</a></li>
                <li><a href="investment-plans.html">Investment Planning</a></li>
                <li><a href="retirement-plans.html">Retirement Planning</a></li>
                <li><a href="term-insurance.html">Term Insurance</a></li>
                <li><a href="#">Tax Advisory</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Resources</h3>
              <ul>
                <li><a href="leadership.html">Leadership</a></li>
                <li><a href="advisor.html">Advisor</a></li>
                <li>
                  <a href="retirement-plans.html">Financial Calculators</a>
                </li>
                <li><a href="faqs.html">FAQs</a></li>
                <li><a href="contact.html">contact</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2023 Growwell Financial Advisors. All rights reserved.</p>
          <ul class="footer-legal">
            <li><a href="privacy-policy.html">Privacy Policy</a></li>
            <li><a href="terms-of-service.html">Terms of Service</a></li>
            <li><a href="cookie-policy.html">Cookie Policy</a></li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Growwell",
        "url": "https://growwellimf.com",
        "logo": "https://growwellimf.com/images/growwell_logo_01.webp",
        "sameAs": [
          "https://www.facebook.com/growwellimf/",
          "https://www.instagram.com/growwell.imf/",
          "https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08",
          "https://www.linkedin.com/company/grow-well-imf/about/"
        ]
      }
    </script>
    <script>
      // FAQ Toggle
      document.querySelectorAll(".faq-question").forEach((question) => {
        question.addEventListener("click", () => {
          const faqItem = question.parentNode;
          faqItem.classList.toggle("active");
        });
      });

      // Premium Calculator
      document
        .getElementById("calculate-btn")
        .addEventListener("click", function () {
          const age = parseInt(document.getElementById("age").value);
          const gender = document.getElementById("gender").value;
          const coverage = parseInt(document.getElementById("coverage").value);
          const term = parseInt(document.getElementById("term").value);
          const smoker = document.getElementById("smoker").value;

          // Simple calculation logic (this would be more complex in a real application)
          let basePremium = (coverage / 1000000) * 500; // ₹500 per million of coverage

          // Age factor
          if (age <= 30) {
            basePremium *= 1;
          } else if (age <= 40) {
            basePremium *= 1.5;
          } else if (age <= 50) {
            basePremium *= 2.2;
          } else {
            basePremium *= 3.5;
          }

          // Gender factor
          if (gender === "female") {
            basePremium *= 0.9; // 10% discount for females
          }

          // Smoker factor
          if (smoker === "yes") {
            basePremium *= 1.5; // 50% extra for smokers
          }

          // Term factor
          if (term > 20) {
            basePremium *= 1.2;
          }

          const monthlyPremium = Math.round(basePremium / 12);
          const annualPremium = Math.round(basePremium);
          const taxBenefit = Math.round(annualPremium * 0.2); // 20% tax benefit

          // Update the result
          document.querySelector(
            ".calculator-result .result-value:nth-of-type(1)"
          ).textContent = `₹${monthlyPremium}`;
          document.querySelector(
            ".calculator-result .result-value:nth-of-type(2)"
          ).textContent = `₹${annualPremium.toLocaleString()}`;
          document.querySelector(
            ".calculator-result .result-value:nth-of-type(3)"
          ).textContent = `₹${taxBenefit.toLocaleString()}`;
        });

      // Range sliders
      const ageSlider = document.getElementById("age-slider");
      const ageInput = document.getElementById("age");

      ageSlider.addEventListener("input", function () {
        ageInput.value = this.value;
      });

      ageInput.addEventListener("input", function () {
        ageSlider.value = this.value;
      });

      const termSlider = document.getElementById("term-slider");
      const termInput = document.getElementById("term");

      termSlider.addEventListener("input", function () {
        termInput.value = this.value;
      });

      termInput.addEventListener("input", function () {
        termSlider.value = this.value;
      });

      document.addEventListener("DOMContentLoaded", function () {
        // Get form fields
        const ageInput = document.getElementById("age");
        const ageSlider = document.getElementById("age-slider");
        const genderInput = document.getElementById("gender");
        const coverageInput = document.getElementById("coverage");
        const termInput = document.getElementById("term");
        const termSlider = document.getElementById("term-slider");
        const smokerInput = document.getElementById("smoker");
        const calculateBtn = document.getElementById("calculate-btn");

        // Get result elements
        const resultCards = document.querySelectorAll(
          ".result-card .result-value"
        );

        // Sync sliders and inputs
        function syncInputs() {
          ageInput.value = ageSlider.value;
          termInput.value = termSlider.value;
        }

        function syncSliders() {
          ageSlider.value = ageInput.value;
          termSlider.value = termInput.value;
        }

        // Simple premium calculation logic
        function calculatePremium() {
          const age = parseInt(ageInput.value) || 30;
          const gender = genderInput.value;
          const coverage = parseInt(coverageInput.value) || 10000000;
          const term = parseInt(termInput.value) || 30;
          const smoker = smokerInput.value;

          let baseRate = 0.0005; // Base monthly rate for ₹1 coverage

          // Adjust base rate based on age
          if (age >= 18 && age <= 30) {
            baseRate *= 1;
          } else if (age <= 40) {
            baseRate *= 1.2;
          } else if (age <= 50) {
            baseRate *= 1.5;
          } else {
            baseRate *= 2;
          }

          // Gender adjustment
          if (gender === "male") {
            baseRate *= 1.1; // Slightly higher for males
          }

          // Smoker adjustment
          if (smoker === "yes") {
            baseRate *= 1.5; // Higher premium for smokers
          }

          // Term adjustment
          if (term < 20) {
            baseRate *= 0.95;
          } else if (term > 30) {
            baseRate *= 1.1;
          }

          // Monthly premium
          const monthlyPremium = baseRate * coverage;
          const annualPremium = monthlyPremium * 12;
          const taxBenefit = Math.min(annualPremium, 150000) * 0.2; // 20% tax benefit, max ₹1.5 lakh premium eligible

          // Format currency
          const formatCurrency = (num) => {
            return "₹" + Math.round(num).toLocaleString("en-IN");
          };

          // Update results
          if (resultCards.length >= 3) {
            resultCards[0].textContent = formatCurrency(monthlyPremium); // Monthly
            resultCards[1].textContent = formatCurrency(annualPremium); // Annual
            resultCards[2].textContent = formatCurrency(taxBenefit); // Tax Benefit
          }
        }

        // Event listeners
        ageSlider.addEventListener("input", syncInputs);
        ageInput.addEventListener("input", syncSliders);
        termSlider.addEventListener("input", syncInputs);
        termInput.addEventListener("input", syncSliders);

        calculateBtn.addEventListener("click", function (e) {
          e.preventDefault();
          calculatePremium();
        });

        // Initial setup
        syncInputs();
      });
    </script>
  </body>
</html>
