<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Track Your Insurance Claim Online | Growwell</title>
    <meta
      name="description"
      content="Easily submit and track your insurance claims with Growwell. Get guided support and quick processing for a smooth and hassle-free claim experience."
    />
    <meta
      name="keywords"
      content="growwell, insurance claims, life insurance, health insurance, vehicle insurance, property insurance, investment plan "
    />
    <meta name="robots" content="track-claim, follow" />
    <link rel="canonical" href="https://growwellimf.com/" />
    <meta
      property="og:title"
      content="Track Your Insurance Claim Online | Growwell"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://growwellimf.com/" />
    <meta
      property="og:image"
      content="https://growwellimf.com/images/growwell_logo_01.webp"
    />
    <meta name="author" content="Growwell" />
    <meta name="publisher" content="Growwell" />

    <!--google adsense-->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>

    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/track-claim.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Header Section -->
    <header>
      <div class="container">
        <div class="navbar">
          <div class="logo">
            <a href="index.html">
              <img
                src="./images/growwell_logo_01.webp"
                alt="company_logo"
                class="brand-logo"
            /></a>
            <a href="index.html"
              ><h2><span class="highlight">Grow</span>well</h2></a
            >
          </div>

          <div class="hamburger" id="hamburger">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
          </div>

          <nav id="nav-menu">
            <ul id="menu-items">
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Products</a>
                <div class="dropdown-menu">
                  <a href="investment-plans.html">Investment Plans</a>
                  <a href="retirement-plans.html">Retirement Plans</a>
                  <a href="child-plans.html">Child Plans</a>
                  <a href="term-insurance.html">Term Insurance</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Renew</a>
                <div class="dropdown-menu">
                  <a href="renew-policy.html">Renew Policy</a>
                  <a href="check-status.html">Check Status</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Claim</a>
                <div class="dropdown-menu">
                  <a href="file-claim.html">File a Claim</a>
                  <a href="track-claim.html">Track Claim</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Help & Support</a>
                <div class="dropdown-menu">
                  <a href="faqs.html">FAQs</a>
                  <a href="contact.html">Contact Us</a>
                  <a href="support.html">Customer Support</a>
                  <a href="advisor.html">Find an Advisor</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">About Us</a>
                <div class="dropdown-menu">
                  <a href="our-story.html">Our Story</a>
                  <a href="leadership.html">Leadership</a>
                  <a href="careers.html">Careers</a>
                </div>
              </li>
            </ul>
          </nav>

          <div class="navbar-actions desktop-only">
            <a href="investment-plans.html" class="btn-primary">Get Started</a>
          </div>
        </div>
      </div>
    </header>

    <!-- Claim Hero Section -->
    <section class="claim-hero">
      <div class="container">
        <div class="claim-hero-content">
          <h1>Track Your Claim</h1>
          <p>
            Monitor the status of your claim and get real-time updates on the
            processing stages.
          </p>
        </div>
      </div>
    </section>

    <!-- Track Claim Section -->
    <section class="track-claim-section">
      <div class="shape-animation claim-shape-1"></div>
      <div class="shape-animation claim-shape-2"></div>

      <div class="container">
        <div class="track-claim-container">
          <div class="section-header">
            <span class="section-subtitle">Claim Tracking</span>
            <h2>Check Your <span class="claim-txt"> Claim Status </span></h2>
            <p>
              Enter your claim reference number to track the current status of
              your claim.
            </p>
          </div>

          <div class="track-form">
            <h3>Track Your Claim</h3>
            <p>
              Enter the claim reference number you received after submitting
              your claim.
            </p>

            <div class="track-input-group">
              <input
                type="text"
                class="track-input"
                id="claimReference"
                placeholder="Enter claim reference number (e.g., CL-123456)"
              />
              <button class="track-btn" id="trackBtn">
                <i class="fas fa-search"></i> Track
              </button>
            </div>

            <p class="track-claim-txt">
              * Your claim reference number was sent to your email after
              submission.
            </p>
          </div>

          <div class="claim-info-card">
            <h3>
              <i class="fas fa-info-circle"></i> Claim Processing Information
            </h3>
            <p>Here's what to expect during the claim processing:</p>
            <ul>
              <li>Document verification typically takes 1-2 business days</li>
              <li>
                Claim assessment may take 3-5 business days depending on
                complexity
              </li>
              <li>
                Final approval is usually completed within 1-2 business days
                after assessment
              </li>
              <li>Payment processing takes 1-3 business days after approval</li>
              <li>
                Total processing time is usually 7-10 business days for standard
                claims
              </li>
            </ul>
          </div>

          <div class="contact-support">
            <h3>Need Help With Your Claim?</h3>
            <p>
              Our customer support team is available to assist you with any
              questions about your claim.
            </p>

            <div class="support-options">
              <a href="tel:+911234567890" class="support-option">
                <i class="fas fa-phone-alt"></i>
                <span>Call Support</span>
              </a>
              <a href="mailto:<EMAIL>" class="support-option">
                <i class="fas fa-envelope"></i>
                <span>Email Claims</span>
              </a>
              <a href="support.html" class="support-option">
                <i class="fas fa-comments"></i>
                <span>Live Chat</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer Section -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <div class="footer-logo-img">
              <img
                src="./images/growwell_logo_01.webp"
                alt="Growwell Logo"
                loading="lazy"
                class="footer-logo-image"
              />
            </div>
            <p>Your partner for financial growth and security</p>
            <div class="social-icons">
              <a
                href="https://www.facebook.com/growwellimf/"
                target="/"
                aria-label="Follow us on Facebook"
                ><i class="fab fa-facebook-f"></i
              ></a>
              <a
                href="https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08"
                target="/"
                aria-label="Follow us on X"
                ><i class="fab fa-twitter"></i
              ></a>
              <a
                href="https://www.linkedin.com/company/grow-well-imf/about/"
                target="/"
                aria-label="Follow us on Linkedin"
                ><i class="fab fa-linkedin-in"></i
              ></a>
              <a
                href="https://www.instagram.com/growwell.imf/"
                target="/"
                aria-label="Follow us on Instagram"
                ><i class="fab fa-instagram"></i
              ></a>
            </div>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h3>Quick Links</h3>
              <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="./investment-plans.html">Products</a></li>
                <li><a href="./renew-policy.html">Renew</a></li>
                <li><a href="./advisor.html">Testimonials</a></li>
                <li><a href="./contact.html">Contact</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Services</h3>
              <ul>
                <li><a href="investment-plans.html">Financial Planning</a></li>
                <li><a href="child-plans.html">Child Planning</a></li>
                <li><a href="investment-plans.html">Investment Planning</a></li>
                <li><a href="retirement-plans.html">Retirement Planning</a></li>
                <li><a href="term-insurance.html">Term Insurance</a></li>
                <li><a href="#">Tax Advisory</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Resources</h3>
              <ul>
                <li><a href="leadership.html">Leadership</a></li>
                <li><a href="advisor.html">Advisor</a></li>
                <li>
                  <a href="retirement-plans.html">Financial Calculators</a>
                </li>
                <li><a href="faqs.html">FAQs</a></li>
                <li><a href="contact.html">contact</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2023 Growwell Financial Advisors. All rights reserved.</p>
          <ul class="footer-legal">
            <li><a href="#">Privacy Policy</a></li>
            <li><a href="#">Terms of Service</a></li>
            <li><a href="#">Cookie Policy</a></li>
          </ul>
        </div>
      </div>
    </footer>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Growwell",
        "url": "https://growwellimf.com",
        "logo": "https://growwellimf.com/images/growwell_logo_01.webp",
        "sameAs": [
          "https://www.facebook.com/growwellimf/",
          "https://www.instagram.com/growwell.imf/",
          "https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08",
          "https://www.linkedin.com/company/grow-well-imf/about/"
        ]
      }
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Mobile Menu Toggle
        const hamburger = document.getElementById("hamburger");
        const navMenu = document.getElementById("nav-menu");
        const body = document.body;

        if (hamburger) {
          hamburger.addEventListener("click", function () {
            navMenu.classList.toggle("active");
            body.classList.toggle("nav-active");

            // Toggle hamburger appearance
            const bars = this.querySelectorAll(".bar");
            bars.forEach((bar) => bar.classList.toggle("active"));

            console.log(
              "Hamburger clicked. Nav active:",
              navMenu.classList.contains("active")
            );
          });
        }

        // Dropdown Toggle for Mobile
        const dropdownToggles = document.querySelectorAll(".dropdown-toggle");
        dropdownToggles.forEach((toggle) => {
          toggle.addEventListener("click", function (e) {
            // Only run on mobile (width <= 992px)
            if (window.innerWidth <= 992) {
              e.preventDefault();
              e.stopPropagation();

              // Find the dropdown menu that is a sibling of this toggle
              const dropdownMenu = this.nextElementSibling;

              // Close all other dropdown menus
              document.querySelectorAll(".dropdown-menu").forEach((menu) => {
                if (menu !== dropdownMenu) {
                  menu.classList.remove("active");
                }
              });

              // Remove active class from all other toggles
              document.querySelectorAll(".dropdown-toggle").forEach((tog) => {
                if (tog !== this) {
                  tog.classList.remove("active");
                }
              });

              // Toggle active class on this dropdown menu and toggle
              dropdownMenu.classList.toggle("active");
              this.classList.toggle("active");
            }
          });
        });

        // Close menu when clicking outside
        document.addEventListener("click", function (e) {
          if (window.innerWidth <= 992) {
            if (!e.target.closest("nav") && !e.target.closest("#hamburger")) {
              // Close the navigation menu
              if (navMenu.classList.contains("active")) {
                navMenu.classList.remove("active");
                body.classList.remove("nav-active");

                // Reset hamburger icon
                const bars = hamburger.querySelectorAll(".bar");
                bars.forEach((bar) => bar.classList.remove("active"));

                // Close all dropdown menus
                document.querySelectorAll(".dropdown-menu").forEach((menu) => {
                  menu.classList.remove("active");
                });

                // Remove active class from all toggles
                document.querySelectorAll(".dropdown-toggle").forEach((tog) => {
                  tog.classList.remove("active");
                });
              }
            }
          }
        });

        // Track claim button functionality
        const trackBtn = document.getElementById("trackBtn");
        const claimReferenceInput = document.getElementById("claimReference");

        if (trackBtn) {
          trackBtn.addEventListener("click", function () {
            const claimReference = claimReferenceInput.value.trim();
            if (claimReference) {
              alert(
                "This is a demo. In a live environment, this would fetch the status of claim: " +
                  claimReference
              );
            } else {
              alert("Please enter a valid claim reference number");
            }
          });
        }
      });
    </script>
  </body>
</html>
